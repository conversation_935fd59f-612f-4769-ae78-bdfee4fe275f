#!/usr/bin/env python3
"""
Verify Maya Bridge Plugin Installation
"""

import sys
import os
from pathlib import Path

def verify_installation():
    """Verify that Maya Bridge plugin is properly installed"""
    print("🔍 Verifying Maya Bridge Plugin Installation...")
    print("=" * 60)
    
    current_dir = Path(__file__).parent
    print(f"Current directory: {current_dir}")
    
    # Check if required files exist
    required_files = [
        "node_wrappers/maya_bridge.py",
        "node_wrappers/maya_bridge_advanced.py", 
        "src/maya_connector.py",
        "src/bone_mapping_presets.py",
        "src/maya_bridge_config.py",
        "maya_scripts/comfyui_bridge_server.py",
        "maya_scripts/depth_buffer_extractor.py"
    ]
    
    print("\n📁 Checking required files:")
    all_files_exist = True
    for file_path in required_files:
        full_path = current_dir / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - MISSING")
            all_files_exist = False
    
    # Check if we can import the modules
    print("\n🐍 Testing Python imports:")
    
    # Add src to path
    src_dir = current_dir / "src"
    if str(src_dir) not in sys.path:
        sys.path.insert(0, str(src_dir))
    
    import_tests = [
        ("bone_mapping_presets", "Bone mapping presets"),
        ("maya_bridge_config", "Maya bridge configuration"),
        ("maya_connector", "Maya connector")
    ]
    
    imports_successful = True
    for module_name, description in import_tests:
        try:
            __import__(module_name)
            print(f"  ✅ {description}")
        except ImportError as e:
            print(f"  ❌ {description} - FAILED: {e}")
            imports_successful = False
        except Exception as e:
            print(f"  ⚠️ {description} - WARNING: {e}")
    
    # Test node wrapper imports (these might fail due to ComfyUI dependencies)
    print("\n🎛️ Testing node wrapper imports:")
    
    # Mock ComfyUI modules for testing
    import types
    sys.modules['comfy'] = types.ModuleType('comfy')
    sys.modules['comfy.model_management'] = types.ModuleType('comfy.model_management')
    sys.modules['comfy.model_management'].get_torch_device = lambda: 'cpu'
    
    # Mock utils
    utils_mock = types.ModuleType('utils')
    utils_mock.common_annotator_call = lambda *args, **kwargs: None
    utils_mock.INPUT = types.ModuleType('INPUT')
    utils_mock.INPUT.STRING = lambda **kwargs: ("STRING", kwargs)
    utils_mock.INPUT.INT = lambda **kwargs: ("INT", kwargs)
    utils_mock.INPUT.FLOAT = lambda **kwargs: ("FLOAT", kwargs)
    utils_mock.INPUT.COMBO = lambda choices, **kwargs: (choices, kwargs)
    utils_mock.INPUT.RESOLUTION = lambda **kwargs: ("INT", {"default": 512, "min": 64, "max": 2048, "step": 64})
    utils_mock.define_preprocessor_inputs = lambda **kwargs: {"required": kwargs}
    
    # Add to parent package
    parent_package = types.ModuleType('parent')
    parent_package.utils = utils_mock
    sys.modules['parent'] = parent_package
    sys.modules['parent.utils'] = utils_mock
    
    # Modify import path for node wrappers
    node_wrapper_dir = current_dir / "node_wrappers"
    if str(node_wrapper_dir) not in sys.path:
        sys.path.insert(0, str(node_wrapper_dir))
    
    node_tests = [
        ("maya_bridge", "Maya Bridge basic node"),
        ("maya_bridge_advanced", "Maya Bridge advanced node")
    ]
    
    nodes_successful = True
    for module_name, description in node_tests:
        try:
            # Try to import with mocked dependencies
            spec = __import__(module_name)
            if hasattr(spec, 'NODE_CLASS_MAPPINGS'):
                print(f"  ✅ {description} - NODE_CLASS_MAPPINGS found")
            else:
                print(f"  ⚠️ {description} - imported but no NODE_CLASS_MAPPINGS")
        except ImportError as e:
            print(f"  ❌ {description} - FAILED: {e}")
            nodes_successful = False
        except Exception as e:
            print(f"  ⚠️ {description} - WARNING: {e}")
    
    # Check dependencies
    print("\n📦 Checking dependencies:")
    dependencies = [
        ("numpy", "NumPy"),
        ("cv2", "OpenCV"),
        ("PIL", "Pillow"),
        ("torch", "PyTorch")
    ]
    
    deps_available = True
    for module_name, description in dependencies:
        try:
            __import__(module_name)
            print(f"  ✅ {description}")
        except ImportError:
            print(f"  ❌ {description} - MISSING")
            deps_available = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 INSTALLATION SUMMARY")
    print("=" * 60)
    
    if all_files_exist:
        print("✅ All required files are present")
    else:
        print("❌ Some required files are missing")
    
    if imports_successful:
        print("✅ Core modules can be imported")
    else:
        print("❌ Some core modules failed to import")
    
    if nodes_successful:
        print("✅ Node wrappers can be imported")
    else:
        print("❌ Some node wrappers failed to import")
    
    if deps_available:
        print("✅ All dependencies are available")
    else:
        print("❌ Some dependencies are missing")
    
    overall_success = all_files_exist and imports_successful and deps_available
    
    if overall_success:
        print("\n🎉 INSTALLATION SUCCESSFUL!")
        print("Maya Bridge Plugin is ready to use.")
        print("\nNext steps:")
        print("1. Restart ComfyUI")
        print("2. Install Maya scripts (see INSTALLATION_GUIDE.md)")
        print("3. Look for Maya Bridge nodes in ControlNet Preprocessors category")
    else:
        print("\n⚠️ INSTALLATION INCOMPLETE")
        print("Please check the errors above and reinstall if necessary.")
    
    return overall_success

if __name__ == "__main__":
    success = verify_installation()
    sys.exit(0 if success else 1)
