#!/usr/bin/env python3
"""
Maya Scripts Installation Helper
Manually install Maya scripts for ComfyUI Bridge
"""

import os
import shutil
import sys
from pathlib import Path

def find_maya_directories():
    """Find Maya installation and scripts directories"""
    maya_dirs = []
    
    if os.name == 'nt':  # Windows
        # Common Maya installation paths
        program_files = [
            Path("C:/Program Files/Autodesk"),
            Path("C:/Program Files (x86)/Autodesk")
        ]
        
        for pf in program_files:
            if pf.exists():
                maya_versions = [d for d in pf.iterdir() if d.is_dir() and d.name.startswith("Maya")]
                maya_dirs.extend(maya_versions)
        
        # User Maya directories
        user_maya_dirs = [
            Path.home() / "Documents" / "maya",
            Path(os.environ.get("MAYA_APP_DIR", "")) if "MAYA_APP_DIR" in os.environ else None
        ]
        
        user_maya_dirs = [d for d in user_maya_dirs if d and d.exists()]
        
        # Find version subdirectories
        for maya_dir in user_maya_dirs:
            version_dirs = [d for d in maya_dir.iterdir() if d.is_dir() and d.name.replace(".", "").isdigit()]
            for version_dir in version_dirs:
                scripts_dir = version_dir / "scripts"
                if scripts_dir.exists():
                    maya_dirs.append(scripts_dir)
    
    else:  # macOS/Linux
        user_maya_dirs = [
            Path.home() / "maya",
            Path.home() / "Library" / "Preferences" / "Autodesk" / "maya" if sys.platform == "darwin" else None
        ]
        
        user_maya_dirs = [d for d in user_maya_dirs if d and d.exists()]
        
        for maya_dir in user_maya_dirs:
            version_dirs = [d for d in maya_dir.iterdir() if d.is_dir() and d.name.replace(".", "").isdigit()]
            for version_dir in version_dirs:
                scripts_dir = version_dir / "scripts"
                if scripts_dir.exists():
                    maya_dirs.append(scripts_dir)
    
    return maya_dirs

def install_maya_scripts():
    """Install Maya scripts"""
    print("🎭 Maya Scripts Installation Helper")
    print("=" * 50)
    
    # Find current directory
    current_dir = Path(__file__).parent
    maya_scripts_dir = current_dir / "maya_scripts"
    
    if not maya_scripts_dir.exists():
        print("❌ Maya scripts directory not found!")
        return False
    
    # Find Maya directories
    maya_dirs = find_maya_directories()
    
    if not maya_dirs:
        print("⚠️ No Maya scripts directories found automatically.")
        print("\nPlease manually copy the following files to your Maya scripts directory:")
        print(f"Source: {maya_scripts_dir}")
        print("Files to copy:")
        for script_file in maya_scripts_dir.iterdir():
            if script_file.is_file() and script_file.suffix == '.py':
                print(f"  - {script_file.name}")
        
        print("\nTypical Maya scripts locations:")
        if os.name == 'nt':
            print("  - C:/Users/<USER>/Documents/maya/[version]/scripts/")
        else:
            print("  - ~/maya/[version]/scripts/")
        
        return False
    
    print(f"Found {len(maya_dirs)} Maya scripts directories:")
    for i, maya_dir in enumerate(maya_dirs):
        print(f"  {i+1}. {maya_dir}")
    
    # Let user choose or install to all
    print("\nOptions:")
    print("  0. Install to all directories")
    for i, maya_dir in enumerate(maya_dirs):
        print(f"  {i+1}. Install to {maya_dir}")
    print("  q. Quit")
    
    choice = input("\nEnter your choice: ").strip().lower()
    
    if choice == 'q':
        print("Installation cancelled.")
        return False
    
    # Determine target directories
    if choice == '0':
        target_dirs = maya_dirs
    else:
        try:
            index = int(choice) - 1
            if 0 <= index < len(maya_dirs):
                target_dirs = [maya_dirs[index]]
            else:
                print("Invalid choice!")
                return False
        except ValueError:
            print("Invalid choice!")
            return False
    
    # Install scripts
    success_count = 0
    for target_dir in target_dirs:
        print(f"\n📁 Installing to: {target_dir}")
        
        try:
            # Copy Maya scripts
            script_files = [
                "comfyui_bridge_server.py",
                "depth_buffer_extractor.py"
            ]
            
            for script_file in script_files:
                src_path = maya_scripts_dir / script_file
                dst_path = target_dir / script_file
                
                if src_path.exists():
                    shutil.copy2(src_path, dst_path)
                    print(f"  ✅ Copied {script_file}")
                else:
                    print(f"  ⚠️ Source file not found: {script_file}")
            
            # Create shelf script
            shelf_script_content = '''"""
ComfyUI Bridge Shelf Setup for Maya
Run this script in Maya to create the ComfyUI Bridge shelf
"""

import maya.cmds as cmds

def create_comfyui_bridge_shelf():
    """Create ComfyUI Bridge shelf"""
    shelf_name = "ComfyUI_Bridge"
    
    # Delete existing shelf if it exists
    if cmds.shelfLayout(shelf_name, exists=True):
        cmds.deleteUI(shelf_name)
    
    # Create new shelf
    shelf = cmds.shelfLayout(shelf_name, parent="ShelfLayout")
    
    # Start Bridge Server button
    cmds.shelfButton(
        parent=shelf,
        label="Start",
        command="exec(open('comfyui_bridge_server.py').read()); start_comfyui_bridge()",
        annotation="Start ComfyUI Bridge Server",
        image="play.png"
    )
    
    # Stop Bridge Server button
    cmds.shelfButton(
        parent=shelf,
        label="Stop",
        command="stop_comfyui_bridge()",
        annotation="Stop ComfyUI Bridge Server", 
        image="stop.png"
    )
    
    # Status button
    cmds.shelfButton(
        parent=shelf,
        label="Status",
        command="print(get_bridge_status())",
        annotation="Check Bridge Server Status",
        image="info.png"
    )
    
    print(f"Created {shelf_name} shelf with ComfyUI Bridge controls")

# Auto-create shelf when script is run
create_comfyui_bridge_shelf()
'''
            
            shelf_script_path = target_dir / "comfyui_bridge_shelf.py"
            with open(shelf_script_path, 'w') as f:
                f.write(shelf_script_content)
            print(f"  ✅ Created comfyui_bridge_shelf.py")
            
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ Failed to install to {target_dir}: {e}")
    
    if success_count > 0:
        print(f"\n✅ Successfully installed to {success_count} Maya directories!")
        print("\n📋 Next steps:")
        print("1. Open Maya")
        print("2. In Maya's Script Editor (Python tab), run:")
        print("   exec(open('comfyui_bridge_shelf.py').read())")
        print("3. This will create a ComfyUI Bridge shelf with control buttons")
        print("4. Click 'Start' to begin the bridge server")
        return True
    else:
        print("\n❌ Installation failed!")
        return False

if __name__ == "__main__":
    install_maya_scripts()
