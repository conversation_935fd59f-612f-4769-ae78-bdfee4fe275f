#!/usr/bin/env python
"""
Maya Bridge Test - ASCII only
"""

import sys
import os

def test_maya_import():
    """Test Maya module import"""
    try:
        import maya.standalone
        print("SUCCESS: maya.standalone imported")
        
        maya.standalone.initialize()
        print("SUCCESS: Maya standalone initialized")
        
        import maya.cmds as cmds
        print("SUCCESS: maya.cmds imported")
        
        # Create new scene
        cmds.file(new=True, force=True)
        print("SUCCESS: New scene created")
        
        # Create test joint
        joint1 = cmds.joint(name='test_joint', position=[0, 0, 0])
        print("SUCCESS: Created joint: " + str(joint1))
        
        # Get joint position
        pos = cmds.xform(joint1, query=True, worldSpace=True, translation=True)
        print("SUCCESS: Joint position: " + str(pos))
        
        # Get all joints
        joints = cmds.ls(type='joint')
        print("SUCCESS: Scene joints: " + str(joints))
        
        maya.standalone.uninitialize()
        print("SUCCESS: Maya standalone cleanup complete")
        
        return True
        
    except Exception as e:
        print("ERROR: Maya test failed: " + str(e))
        return False

def test_socket_server():
    """Test Socket server"""
    try:
        import socket
        import threading
        import time
        
        print("Testing Socket server...")
        
        # Create server
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_socket.bind(('localhost', 7001))
        server_socket.listen(1)
        
        print("SUCCESS: Socket server created on port 7001")
        
        # Test connection
        def test_client():
            time.sleep(1)  # Wait for server to start
            try:
                client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                client.connect(('localhost', 7001))
                client.send(b'test')
                client.close()
                print("SUCCESS: Client connection test passed")
            except Exception as e:
                print("ERROR: Client connection failed: " + str(e))
        
        # Start client test thread
        client_thread = threading.Thread(target=test_client)
        client_thread.start()
        
        # Accept connection
        client_socket, address = server_socket.accept()
        data = client_socket.recv(1024)
        print("SUCCESS: Server received data: " + str(data))
        
        client_socket.close()
        server_socket.close()
        client_thread.join()
        
        print("SUCCESS: Socket test complete")
        return True
        
    except Exception as e:
        print("ERROR: Socket test failed: " + str(e))
        return False

def main():
    """Main test function"""
    print("Maya Bridge Simple Test Starting...")
    print("=" * 50)
    
    # Test Maya
    print("\nTest 1: Maya module import and basic functionality")
    maya_ok = test_maya_import()
    
    # Test Socket
    print("\nTest 2: Socket server functionality")
    socket_ok = test_socket_server()
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print("Maya functionality: " + ("PASS" if maya_ok else "FAIL"))
    print("Socket functionality: " + ("PASS" if socket_ok else "FAIL"))
    
    if maya_ok and socket_ok:
        print("\nSUCCESS: All tests passed! Maya Bridge plugin can work properly.")
        return True
    else:
        print("\nERROR: Some tests failed, please check error messages.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
