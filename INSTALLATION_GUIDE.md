# Maya Bridge Plugin 安装指南

## 🎉 安装状态

✅ **ComfyUI端已成功安装！**

Maya Bridge插件的ComfyUI部分已经成功安装到您的系统中。现在您需要完成Maya端的设置。

## 📋 安装完成情况

### ✅ 已完成
- ComfyUI节点文件已安装
- 依赖包已安装 (numpy, opencv-python, Pillow, torch, torchvision)
- 配置文件已创建
- 插件已集成到ComfyUI中

### 🔄 需要手动完成
- Maya端脚本安装
- Maya货架创建

## 🎭 Maya端安装步骤

### 步骤1: 找到Maya脚本目录

Maya脚本目录通常位于：

**Windows:**
```
C:\Users\<USER>\Documents\maya\[Maya版本]\scripts\
```

例如：
- `C:\Users\<USER>\Documents\maya\2024\scripts\`
- `C:\Users\<USER>\Documents\maya\2023\scripts\`

**macOS:**
```
~/Library/Preferences/Autodesk/maya/[Maya版本]/scripts/
```

**Linux:**
```
~/maya/[Maya版本]/scripts/
```

### 步骤2: 复制Maya脚本文件

将以下文件从插件目录复制到Maya脚本目录：

**源文件位置:**
```
d:\Softwear\ComfyUI-aki\ComfyUI-aki-v1.6\ComfyUI\custom_nodes\comfyui_controlnet_aux\maya_scripts\
```

**需要复制的文件:**
1. `comfyui_bridge_server.py` - Maya桥接服务器
2. `depth_buffer_extractor.py` - 深度缓冲区提取器

### 步骤3: 创建Maya货架

1. 打开Maya
2. 打开Script Editor (Windows > General Editors > Script Editor)
3. 在Python标签页中粘贴以下代码：

```python
"""
ComfyUI Bridge Shelf Setup for Maya
"""
import maya.cmds as cmds

def create_comfyui_bridge_shelf():
    """Create ComfyUI Bridge shelf"""
    shelf_name = "ComfyUI_Bridge"
    
    # Delete existing shelf if it exists
    if cmds.shelfLayout(shelf_name, exists=True):
        cmds.deleteUI(shelf_name)
    
    # Create new shelf
    shelf = cmds.shelfLayout(shelf_name, parent="ShelfLayout")
    
    # Start Bridge Server button
    cmds.shelfButton(
        parent=shelf,
        label="Start",
        command="exec(open('comfyui_bridge_server.py').read()); start_comfyui_bridge()",
        annotation="Start ComfyUI Bridge Server",
        image="play.png"
    )
    
    # Stop Bridge Server button
    cmds.shelfButton(
        parent=shelf,
        label="Stop", 
        command="stop_comfyui_bridge()",
        annotation="Stop ComfyUI Bridge Server",
        image="stop.png"
    )
    
    # Status button
    cmds.shelfButton(
        parent=shelf,
        label="Status",
        command="print(get_bridge_status())",
        annotation="Check Bridge Server Status",
        image="info.png"
    )
    
    print(f"Created {shelf_name} shelf with ComfyUI Bridge controls")

# Create the shelf
create_comfyui_bridge_shelf()
```

4. 执行代码 (Ctrl+Enter 或点击Execute按钮)
5. 您应该会看到一个新的"ComfyUI_Bridge"货架出现

## 🚀 使用方法

### 启动Maya Bridge

1. **在Maya中:**
   - 点击ComfyUI_Bridge货架上的"Start"按钮
   - 或在Script Editor中运行: `start_comfyui_bridge()`
   - 您应该看到消息: "ComfyUI Bridge Server started on port 7001"

2. **在ComfyUI中:**
   - 重启ComfyUI (如果还没有重启的话)
   - 在节点菜单中找到: `ControlNet Preprocessors > Maya Bridge`
   - 可用节点:
     - `Maya Bridge (Pose + Depth)` - 基础功能
     - `Maya Bridge Advanced` - 高级功能

### 基本工作流程

1. 在Maya中设置您的场景和角色
2. 启动Maya Bridge服务器
3. 在ComfyUI中添加Maya Bridge节点
4. 配置连接设置:
   - Maya Host: `localhost` (如果Maya和ComfyUI在同一台机器上)
   - Maya Port: `7001` (默认端口)
5. 选择骨骼映射预设或自定义映射
6. 运行ComfyUI工作流

## 🔧 配置选项

### 骨骼映射预设
- **Standard**: 通用Maya关节命名
- **Mixamo**: Adobe Mixamo角色
- **HumanIK**: Maya HumanIK系统
- **Advanced Skeleton**: Advanced Skeleton插件
- **Custom**: 自定义JSON映射

### 连接设置
- **Maya Host**: Maya服务器地址 (默认: localhost)
- **Maya Port**: 通信端口 (默认: 7001)
- **Connection Timeout**: 连接超时时间 (默认: 5秒)

### 输出选项
- **Combined**: 姿态图像 + 深度图像
- **Pose Only**: 仅姿态图像
- **Depth Only**: 仅深度图像

## 🐛 故障排除

### 常见问题

1. **"Failed to connect to Maya"**
   - 确保Maya Bridge服务器正在运行
   - 检查防火墙设置是否允许端口7001
   - 确认Maya和ComfyUI在同一网络上

2. **"Missing bone: [bone_name]"**
   - 启用"Ignore Missing Bones"选项
   - 使用"Auto Detect Bones"功能
   - 为您的骨骼创建自定义映射

3. **Maya脚本错误**
   - 确保脚本文件在正确的Maya scripts目录中
   - 检查Maya的Python环境
   - 重启Maya后重试

4. **ComfyUI中找不到节点**
   - 重启ComfyUI
   - 检查控制台是否有错误消息
   - 确认插件文件已正确安装

### 调试模式

在Maya Bridge节点中启用"Debug Mode"可以获得详细的状态信息，帮助诊断问题。

## 📞 获取帮助

如果遇到问题：
1. 检查ComfyUI控制台的错误消息
2. 在Maya Script Editor中查看Python错误
3. 启用调试模式获取详细信息
4. 检查防火墙和网络设置

## 🎊 安装完成！

恭喜！您的Maya Bridge插件现在已经准备就绪。您可以开始使用ComfyUI作为Maya的强大渲染器了！

享受3D动画与AI渲染技术结合带来的无限创作可能！
