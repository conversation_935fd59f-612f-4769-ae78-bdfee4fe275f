"""
Bone Mapping Presets for Maya to OpenPose conversion
Supports various Maya rig naming conventions
"""

import json
from typing import Dict, List, Optional, Tuple

# OpenPose COCO 18 keypoints with descriptions
OPENPOSE_KEYPOINTS_INFO = {
    0: {"name": "Nose", "description": "Nose tip or head center"},
    1: {"name": "Neck", "description": "Base of neck"},
    2: {"name": "RShoulder", "description": "Right shoulder joint"},
    3: {"name": "REl<PERSON>", "description": "Right elbow joint"},
    4: {"name": "<PERSON><PERSON><PERSON>", "description": "Right wrist joint"},
    5: {"name": "<PERSON><PERSON>houlder", "description": "Left shoulder joint"},
    6: {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Left elbow joint"},
    7: {"name": "<PERSON><PERSON><PERSON>", "description": "Left wrist joint"},
    8: {"name": "RHip", "description": "Right hip joint"},
    9: {"name": "<PERSON><PERSON><PERSON>", "description": "Right knee joint"},
    10: {"name": "<PERSON><PERSON><PERSON>", "description": "Right ankle joint"},
    11: {"name": "<PERSON>H<PERSON>", "description": "Left hip joint"},
    12: {"name": "<PERSON><PERSON><PERSON>", "description": "Left knee joint"},
    13: {"name": "LAnkle", "description": "Left ankle joint"},
    14: {"name": "REye", "description": "Right eye"},
    15: {"name": "LEye", "description": "Left eye"},
    16: {"name": "REar", "description": "Right ear"},
    17: {"name": "LEar", "description": "Left ear"}
}

# Standard Maya rig naming conventions
MAYA_RIG_PRESETS = {
    "standard": {
        "name": "Standard Maya Rig",
        "description": "Common Maya joint naming convention",
        "mapping": {
            "head": 0,
            "neck": 1,
            "rightShoulder": 2,
            "rightElbow": 3,
            "rightWrist": 4,
            "leftShoulder": 5,
            "leftElbow": 6,
            "leftWrist": 7,
            "rightHip": 8,
            "rightKnee": 9,
            "rightAnkle": 10,
            "leftHip": 11,
            "leftKnee": 12,
            "leftAnkle": 13,
            "rightEye": 14,
            "leftEye": 15,
            "rightEar": 16,
            "leftEar": 17
        }
    },
    
    "mixamo": {
        "name": "Mixamo Rig",
        "description": "Adobe Mixamo character rig",
        "mapping": {
            "mixamorig:Head": 0,
            "mixamorig:Neck": 1,
            "mixamorig:RightShoulder": 2,
            "mixamorig:RightArm": 3,
            "mixamorig:RightHand": 4,
            "mixamorig:LeftShoulder": 5,
            "mixamorig:LeftArm": 6,
            "mixamorig:LeftHand": 7,
            "mixamorig:RightUpLeg": 8,
            "mixamorig:RightLeg": 9,
            "mixamorig:RightFoot": 10,
            "mixamorig:LeftUpLeg": 11,
            "mixamorig:LeftLeg": 12,
            "mixamorig:LeftFoot": 13,
            "mixamorig:RightEye": 14,
            "mixamorig:LeftEye": 15,
            "mixamorig:Head": 16,  # Approximate ear position
            "mixamorig:Head": 17   # Approximate ear position
        }
    },
    
    "humanik": {
        "name": "HumanIK Rig",
        "description": "Maya HumanIK character rig",
        "mapping": {
            "Head": 0,
            "Neck": 1,
            "RightShoulder": 2,
            "RightArm": 3,
            "RightHand": 4,
            "LeftShoulder": 5,
            "LeftArm": 6,
            "LeftHand": 7,
            "RightUpLeg": 8,
            "RightLeg": 9,
            "RightFoot": 10,
            "LeftUpLeg": 11,
            "LeftLeg": 12,
            "LeftFoot": 13,
            "RightEye": 14,
            "LeftEye": 15,
            "RightEar": 16,
            "LeftEar": 17
        }
    },
    
    "advanced_skeleton": {
        "name": "Advanced Skeleton",
        "description": "Advanced Skeleton rig system",
        "mapping": {
            "Head_M": 0,
            "Neck_M": 1,
            "Scapula_R": 2,
            "Shoulder_R": 3,
            "Elbow_R": 4,
            "Scapula_L": 5,
            "Shoulder_L": 6,
            "Elbow_L": 7,
            "Hip_R": 8,
            "Knee_R": 9,
            "Ankle_R": 10,
            "Hip_L": 11,
            "Knee_L": 12,
            "Ankle_L": 13,
            "Eye_R": 14,
            "Eye_L": 15,
            "Ear_R": 16,
            "Ear_L": 17
        }
    },
    
    "mhx2": {
        "name": "MHX2 Rig",
        "description": "MakeHuman MHX2 rig format",
        "mapping": {
            "head": 0,
            "neck": 1,
            "shoulder.R": 2,
            "upper_arm.R": 3,
            "hand.R": 4,
            "shoulder.L": 5,
            "upper_arm.L": 6,
            "hand.L": 7,
            "thigh.R": 8,
            "shin.R": 9,
            "foot.R": 10,
            "thigh.L": 11,
            "shin.L": 12,
            "foot.L": 13,
            "eye.R": 14,
            "eye.L": 15,
            "ear.R": 16,
            "ear.L": 17
        }
    }
}

class BoneMappingManager:
    """Manages bone mapping configurations and conversions"""
    
    def __init__(self):
        self.presets = MAYA_RIG_PRESETS.copy()
        self.custom_mappings = {}
    
    def get_preset_names(self) -> List[str]:
        """Get list of available preset names"""
        return list(self.presets.keys())
    
    def get_preset(self, name: str) -> Optional[Dict]:
        """Get preset by name"""
        return self.presets.get(name)
    
    def add_custom_mapping(self, name: str, mapping: Dict[str, int], 
                          description: str = "Custom mapping"):
        """Add custom bone mapping"""
        self.custom_mappings[name] = {
            "name": name,
            "description": description,
            "mapping": mapping
        }
    
    def validate_mapping(self, mapping: Dict[str, int]) -> Tuple[bool, List[str]]:
        """Validate bone mapping configuration"""
        errors = []
        
        # Check for valid OpenPose indices
        for bone_name, openpose_idx in mapping.items():
            if not isinstance(openpose_idx, int):
                errors.append(f"Invalid index type for {bone_name}: {type(openpose_idx)}")
            elif openpose_idx < 0 or openpose_idx >= 18:
                errors.append(f"Invalid OpenPose index for {bone_name}: {openpose_idx}")
        
        # Check for duplicate indices
        indices = list(mapping.values())
        duplicates = set([x for x in indices if indices.count(x) > 1])
        if duplicates:
            errors.append(f"Duplicate OpenPose indices: {duplicates}")
        
        return len(errors) == 0, errors
    
    def create_mapping_from_json(self, json_str: str) -> Tuple[Optional[Dict], List[str]]:
        """Create mapping from JSON string"""
        try:
            mapping = json.loads(json_str)
            is_valid, errors = self.validate_mapping(mapping)
            
            if is_valid:
                return mapping, []
            else:
                return None, errors
                
        except json.JSONDecodeError as e:
            return None, [f"JSON parsing error: {str(e)}"]
    
    def suggest_bone_names(self, available_bones: List[str], 
                          preset_name: str = "standard") -> Dict[str, List[str]]:
        """Suggest bone name matches for available Maya bones"""
        preset = self.get_preset(preset_name)
        if not preset:
            return {}
        
        suggestions = {}
        preset_bones = list(preset["mapping"].keys())
        
        # Simple fuzzy matching
        for openpose_idx, info in OPENPOSE_KEYPOINTS_INFO.items():
            openpose_name = info["name"].lower()
            matches = []
            
            for bone in available_bones:
                bone_lower = bone.lower()
                
                # Direct name matching
                if openpose_name in bone_lower or bone_lower in openpose_name:
                    matches.append(bone)
                
                # Pattern matching for common variations
                patterns = self._get_bone_patterns(openpose_name)
                for pattern in patterns:
                    if pattern in bone_lower:
                        matches.append(bone)
            
            if matches:
                suggestions[info["name"]] = list(set(matches))
        
        return suggestions
    
    def _get_bone_patterns(self, openpose_name: str) -> List[str]:
        """Get common bone name patterns for OpenPose keypoint"""
        patterns = {
            "nose": ["head", "skull", "nose"],
            "neck": ["neck", "cervical"],
            "rshoulder": ["right", "shoulder", "r_shoulder", "shoulder_r"],
            "relbow": ["right", "elbow", "r_elbow", "elbow_r", "arm"],
            "rwrist": ["right", "wrist", "r_wrist", "wrist_r", "hand"],
            "lshoulder": ["left", "shoulder", "l_shoulder", "shoulder_l"],
            "lelbow": ["left", "elbow", "l_elbow", "elbow_l", "arm"],
            "lwrist": ["left", "wrist", "l_wrist", "wrist_l", "hand"],
            "rhip": ["right", "hip", "r_hip", "hip_r", "pelvis"],
            "rknee": ["right", "knee", "r_knee", "knee_r", "leg"],
            "rankle": ["right", "ankle", "r_ankle", "ankle_r", "foot"],
            "lhip": ["left", "hip", "l_hip", "hip_l", "pelvis"],
            "lknee": ["left", "knee", "l_knee", "knee_l", "leg"],
            "lankle": ["left", "ankle", "l_ankle", "ankle_l", "foot"],
            "reye": ["right", "eye", "r_eye", "eye_r"],
            "leye": ["left", "eye", "l_eye", "eye_l"],
            "rear": ["right", "ear", "r_ear", "ear_r"],
            "lear": ["left", "ear", "l_ear", "ear_l"]
        }
        
        return patterns.get(openpose_name.lower(), [openpose_name.lower()])
    
    def export_mapping_to_json(self, mapping: Dict[str, int], 
                              indent: int = 2) -> str:
        """Export mapping to JSON string"""
        return json.dumps(mapping, indent=indent, sort_keys=True)
    
    def get_openpose_info(self, index: int) -> Optional[Dict]:
        """Get OpenPose keypoint information by index"""
        return OPENPOSE_KEYPOINTS_INFO.get(index)
    
    def get_all_openpose_info(self) -> Dict[int, Dict]:
        """Get all OpenPose keypoint information"""
        return OPENPOSE_KEYPOINTS_INFO.copy()

# Global instance
bone_mapping_manager = BoneMappingManager()

def get_bone_mapping_manager() -> BoneMappingManager:
    """Get global bone mapping manager instance"""
    return bone_mapping_manager
