"""
Maya Connector Module
Handles communication between ComfyUI and Maya for real-time data exchange
"""

import socket
import json
import struct
import threading
import time
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import base64
from PIL import Image
import io

class MayaConnector:
    """Handles connection and data exchange with Maya"""
    
    def __init__(self, host: str = "localhost", port: int = 7001):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        self.timeout = 5.0
        
    def connect(self) -> bool:
        """Establish connection to Maya"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            self.socket.connect((self.host, self.port))
            self.connected = True
            return True
        except Exception as e:
            print(f"Failed to connect to Maya: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """Close connection to Maya"""
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        self.connected = False
    
    def send_command(self, command: str, data: Dict = None) -> Optional[Dict]:
        """Send command to Maya and receive response"""
        if not self.connected:
            if not self.connect():
                return None
        
        try:
            # Prepare message
            message = {
                "command": command,
                "data": data or {}
            }
            
            # Send message
            message_json = json.dumps(message)
            message_bytes = message_json.encode('utf-8')
            message_length = struct.pack('!I', len(message_bytes))
            
            self.socket.sendall(message_length + message_bytes)
            
            # Receive response
            response_length_bytes = self._recv_exact(4)
            if not response_length_bytes:
                return None
                
            response_length = struct.unpack('!I', response_length_bytes)[0]
            response_bytes = self._recv_exact(response_length)
            
            if not response_bytes:
                return None
                
            response = json.loads(response_bytes.decode('utf-8'))
            return response
            
        except Exception as e:
            print(f"Communication error: {e}")
            self.disconnect()
            return None
    
    def _recv_exact(self, length: int) -> Optional[bytes]:
        """Receive exact number of bytes"""
        data = b''
        while len(data) < length:
            chunk = self.socket.recv(length - len(data))
            if not chunk:
                return None
            data += chunk
        return data
    
    def get_viewport_image(self, width: int = 512, height: int = 512) -> Optional[np.ndarray]:
        """Get current viewport image from Maya"""
        response = self.send_command("get_viewport_image", {
            "width": width,
            "height": height
        })
        
        if response and response.get("success"):
            # Decode base64 image data
            image_data = response.get("image_data")
            if image_data:
                image_bytes = base64.b64decode(image_data)
                image = Image.open(io.BytesIO(image_bytes))
                return np.array(image)
        
        return None
    
    def get_bone_positions(self, bone_names: List[str] = None) -> Dict[str, Tuple[float, float, float]]:
        """Get world positions of specified bones"""
        response = self.send_command("get_bone_positions", {
            "bone_names": bone_names
        })
        
        if response and response.get("success"):
            return response.get("bone_positions", {})
        
        return {}
    
    def get_camera_info(self) -> Dict[str, Any]:
        """Get current camera information"""
        response = self.send_command("get_camera_info")
        
        if response and response.get("success"):
            return response.get("camera_info", {})
        
        return {}
    
    def get_depth_buffer(self, width: int = 512, height: int = 512) -> Optional[np.ndarray]:
        """Get depth buffer from Maya viewport"""
        response = self.send_command("get_depth_buffer", {
            "width": width,
            "height": height
        })
        
        if response and response.get("success"):
            # Decode depth data
            depth_data = response.get("depth_data")
            if depth_data:
                depth_array = np.array(depth_data, dtype=np.float32)
                return depth_array.reshape((height, width))
        
        return None
    
    def get_scene_data(self, bone_names: List[str] = None, 
                      image_size: Tuple[int, int] = (512, 512)) -> Dict[str, Any]:
        """Get comprehensive scene data from Maya"""
        width, height = image_size
        
        # Get all data in one call for efficiency
        response = self.send_command("get_scene_data", {
            "bone_names": bone_names,
            "image_width": width,
            "image_height": height,
            "include_viewport": True,
            "include_depth": True
        })
        
        if response and response.get("success"):
            data = response.get("data", {})
            
            # Process viewport image
            viewport_image = None
            if data.get("viewport_image_data"):
                image_bytes = base64.b64decode(data["viewport_image_data"])
                viewport_image = np.array(Image.open(io.BytesIO(image_bytes)))
            
            # Process depth buffer
            depth_buffer = None
            if data.get("depth_data"):
                depth_buffer = np.array(data["depth_data"], dtype=np.float32)
                depth_buffer = depth_buffer.reshape((height, width))
            
            return {
                "viewport_image": viewport_image,
                "bone_positions": data.get("bone_positions", {}),
                "camera_info": data.get("camera_info", {}),
                "depth_buffer": depth_buffer,
                "timestamp": data.get("timestamp", time.time())
            }
        
        return {}


class MayaScriptGenerator:
    """Generates Maya MEL/Python scripts for data extraction"""
    
    @staticmethod
    def get_viewport_capture_script(width: int = 512, height: int = 512) -> str:
        """Generate script to capture viewport image"""
        return f"""
import maya.cmds as cmds
import maya.OpenMayaUI as omui
import base64
import tempfile
import os

def capture_viewport():
    # Get active viewport
    view = omui.M3dView.active3dView()
    
    # Capture viewport
    temp_file = tempfile.mktemp(suffix='.jpg')
    
    # Set viewport size and capture
    cmds.playblast(
        filename=temp_file,
        viewer=False,
        showOrnaments=False,
        frame=[cmds.currentTime(query=True)],
        widthHeight=[{width}, {height}],
        percent=100,
        compression='jpg',
        quality=90
    )
    
    # Read and encode image
    with open(temp_file, 'rb') as f:
        image_data = base64.b64encode(f.read()).decode('utf-8')
    
    # Clean up
    os.remove(temp_file)
    
    return image_data
"""
    
    @staticmethod
    def get_bone_positions_script(bone_names: List[str]) -> str:
        """Generate script to get bone world positions"""
        bone_list = str(bone_names) if bone_names else "[]"
        
        return f"""
import maya.cmds as cmds

def get_bone_positions():
    bone_names = {bone_list}
    positions = {{}}
    
    # If no specific bones specified, get all joints
    if not bone_names:
        bone_names = cmds.ls(type='joint')
    
    for bone in bone_names:
        if cmds.objExists(bone):
            # Get world space position
            pos = cmds.xform(bone, query=True, worldSpace=True, translation=True)
            positions[bone] = pos
    
    return positions
"""
    
    @staticmethod
    def get_camera_info_script() -> str:
        """Generate script to get camera information"""
        return """
import maya.cmds as cmds

def get_camera_info():
    # Get current camera
    panel = cmds.getPanel(withFocus=True)
    if cmds.getPanel(typeOf=panel) == 'modelPanel':
        camera = cmds.modelPanel(panel, query=True, camera=True)
    else:
        camera = 'persp'  # Default camera
    
    # Get camera transform and shape
    camera_transform = camera
    camera_shapes = cmds.listRelatives(camera_transform, shapes=True, type='camera')
    
    if not camera_shapes:
        return {}
    
    camera_shape = camera_shapes[0]
    
    # Get camera attributes
    info = {
        'name': camera_transform,
        'position': cmds.xform(camera_transform, query=True, worldSpace=True, translation=True),
        'rotation': cmds.xform(camera_transform, query=True, worldSpace=True, rotation=True),
        'focal_length': cmds.getAttr(camera_shape + '.focalLength'),
        'horizontal_film_aperture': cmds.getAttr(camera_shape + '.horizontalFilmAperture'),
        'vertical_film_aperture': cmds.getAttr(camera_shape + '.verticalFilmAperture'),
        'near_clip': cmds.getAttr(camera_shape + '.nearClipPlane'),
        'far_clip': cmds.getAttr(camera_shape + '.farClipPlane')
    }
    
    return info
"""
    
    @staticmethod
    def get_depth_buffer_script(width: int = 512, height: int = 512) -> str:
        """Generate script to get depth buffer"""
        return f"""
import maya.cmds as cmds
import maya.OpenMaya as om
import maya.OpenMayaRender as omr

def get_depth_buffer():
    # This is a simplified version - actual implementation would require
    # more complex Maya API calls to access the depth buffer
    
    # For now, return empty depth data
    # In a full implementation, you would:
    # 1. Get the current viewport
    # 2. Access the depth buffer through OpenGL
    # 3. Convert to numpy array format
    
    width, height = {width}, {height}
    depth_data = [0.0] * (width * height)
    
    return depth_data
"""


# Global connector instance
_maya_connector = None

def get_maya_connector(host: str = "localhost", port: int = 7001) -> MayaConnector:
    """Get or create Maya connector instance"""
    global _maya_connector
    
    if _maya_connector is None or _maya_connector.host != host or _maya_connector.port != port:
        if _maya_connector:
            _maya_connector.disconnect()
        _maya_connector = MayaConnector(host, port)
    
    return _maya_connector

def cleanup_maya_connector():
    """Clean up Maya connector"""
    global _maya_connector
    if _maya_connector:
        _maya_connector.disconnect()
        _maya_connector = None
