#!/usr/bin/env python
"""
Test ComfyUI Maya Bridge Connection
"""

import sys
import os
import socket
import json
import struct
import time
import numpy as np
from PIL import Image

# Add src directory to path
current_dir = os.path.dirname(__file__)
src_dir = os.path.join(current_dir, 'src')
if src_dir not in sys.path:
    sys.path.append(src_dir)

def test_maya_connector():
    """Test Maya connector module"""
    try:
        from maya_connector import MayaConnector
        
        print("SUCCESS: Maya connector imported")
        
        # Create connector
        connector = MayaConnector("localhost", 7001)
        print("SUCCESS: Maya connector created")
        
        return connector
        
    except ImportError as e:
        print("ERROR: Failed to import maya_connector: " + str(e))
        return None
    except Exception as e:
        print("ERROR: Failed to create maya_connector: " + str(e))
        return None

def test_bone_mapping():
    """Test bone mapping presets"""
    try:
        from bone_mapping_presets import get_bone_mapping_manager
        
        manager = get_bone_mapping_manager()
        print("SUCCESS: Bone mapping manager imported")
        
        # Test presets
        presets = manager.get_preset_names()
        print("Available presets: " + str(presets))
        
        # Test standard preset
        standard = manager.get_preset("standard")
        if standard:
            print("SUCCESS: Standard preset loaded with " + str(len(standard["mapping"])) + " bones")
        
        return manager
        
    except ImportError as e:
        print("ERROR: Failed to import bone_mapping_presets: " + str(e))
        return None
    except Exception as e:
        print("ERROR: Bone mapping test failed: " + str(e))
        return None

def test_maya_bridge_node():
    """Test Maya Bridge node"""
    try:
        # Mock ComfyUI dependencies
        import types
        sys.modules['comfy'] = types.ModuleType('comfy')
        sys.modules['comfy.model_management'] = types.ModuleType('comfy.model_management')
        sys.modules['comfy.model_management'].get_torch_device = lambda: 'cpu'
        
        # Mock utils
        utils_mock = types.ModuleType('utils')
        utils_mock.common_annotator_call = lambda *args, **kwargs: None
        utils_mock.INPUT = types.ModuleType('INPUT')
        utils_mock.INPUT.STRING = lambda **kwargs: ("STRING", kwargs)
        utils_mock.INPUT.INT = lambda **kwargs: ("INT", kwargs)
        utils_mock.INPUT.FLOAT = lambda **kwargs: ("FLOAT", kwargs)
        utils_mock.INPUT.COMBO = lambda choices, **kwargs: (choices, kwargs)
        utils_mock.INPUT.RESOLUTION = lambda **kwargs: ("INT", {"default": 512, "min": 64, "max": 2048, "step": 64})
        utils_mock.define_preprocessor_inputs = lambda **kwargs: {"required": kwargs}
        
        # Add to parent package
        parent_package = types.ModuleType('parent')
        parent_package.utils = utils_mock
        sys.modules['parent'] = parent_package
        sys.modules['parent.utils'] = utils_mock
        
        # Import node
        node_wrapper_dir = os.path.join(current_dir, 'node_wrappers')
        if node_wrapper_dir not in sys.path:
            sys.path.append(node_wrapper_dir)
        
        # Try to import the node
        import maya_bridge
        
        if hasattr(maya_bridge, 'MayaBridgePreprocessor'):
            print("SUCCESS: Maya Bridge node imported")
            
            # Test input types
            input_types = maya_bridge.MayaBridgePreprocessor.INPUT_TYPES()
            print("SUCCESS: Node input types defined")
            
            return maya_bridge.MayaBridgePreprocessor
        else:
            print("ERROR: MayaBridgePreprocessor not found in module")
            return None
        
    except ImportError as e:
        print("ERROR: Failed to import Maya Bridge node: " + str(e))
        return None
    except Exception as e:
        print("ERROR: Maya Bridge node test failed: " + str(e))
        return None

def test_full_workflow():
    """Test full Maya Bridge workflow"""
    print("Testing full Maya Bridge workflow...")
    
    # Start a mock Maya server for testing
    def start_mock_maya_server():
        """Start a mock Maya server"""
        import threading
        
        def mock_server():
            try:
                server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                server_socket.bind(('localhost', 7002))  # Use different port
                server_socket.listen(1)
                
                print("Mock Maya server started on port 7002")
                
                client_socket, address = server_socket.accept()
                
                # Receive request
                length_data = client_socket.recv(4)
                if length_data:
                    message_length = struct.unpack('!I', length_data)[0]
                    message_data = client_socket.recv(message_length)
                    
                    # Send mock response
                    mock_response = {
                        "success": True,
                        "data": {
                            "timestamp": time.time(),
                            "bone_positions": {
                                "head": [0.0, 8.0, 0.0],
                                "neck": [0.0, 7.0, 0.0],
                                "rightShoulder": [2.0, 6.5, 0.0],
                                "leftShoulder": [-2.0, 6.5, 0.0],
                                "rightElbow": [3.5, 5.0, 0.0],
                                "leftElbow": [-3.5, 5.0, 0.0]
                            },
                            "camera_info": {},
                            "viewport_image": None,
                            "depth_buffer": None
                        }
                    }
                    
                    response_json = json.dumps(mock_response)
                    response_bytes = response_json.encode('utf-8')
                    response_length = struct.pack('!I', len(response_bytes))
                    
                    client_socket.sendall(response_length + response_bytes)
                
                client_socket.close()
                server_socket.close()
                print("Mock Maya server completed")
                
            except Exception as e:
                print("Mock server error: " + str(e))
        
        server_thread = threading.Thread(target=mock_server)
        server_thread.daemon = True
        server_thread.start()
        return server_thread
    
    # Start mock server
    server_thread = start_mock_maya_server()
    time.sleep(1)  # Wait for server to start
    
    # Test connector
    try:
        from maya_connector import MayaConnector
        
        connector = MayaConnector("localhost", 7002)
        scene_data = connector.get_scene_data()
        
        if scene_data and scene_data.get("bone_positions"):
            print("SUCCESS: Full workflow test passed!")
            print("Retrieved bone positions: " + str(len(scene_data["bone_positions"])))
            return True
        else:
            print("ERROR: No scene data received")
            return False
            
    except Exception as e:
        print("ERROR: Full workflow test failed: " + str(e))
        return False

def main():
    """Main test function"""
    print("ComfyUI Maya Bridge Connection Test")
    print("=" * 50)
    
    # Test 1: Maya connector
    print("\nTest 1: Maya Connector Module")
    connector = test_maya_connector()
    
    # Test 2: Bone mapping
    print("\nTest 2: Bone Mapping Presets")
    bone_manager = test_bone_mapping()
    
    # Test 3: Maya Bridge node
    print("\nTest 3: Maya Bridge Node")
    bridge_node = test_maya_bridge_node()
    
    # Test 4: Full workflow
    print("\nTest 4: Full Workflow")
    workflow_ok = test_full_workflow()
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print("Maya Connector: " + ("PASS" if connector else "FAIL"))
    print("Bone Mapping: " + ("PASS" if bone_manager else "FAIL"))
    print("Bridge Node: " + ("PASS" if bridge_node else "FAIL"))
    print("Full Workflow: " + ("PASS" if workflow_ok else "FAIL"))
    
    all_passed = all([connector, bone_manager, bridge_node, workflow_ok])
    
    if all_passed:
        print("\nSUCCESS: All ComfyUI tests passed!")
        print("Maya Bridge plugin is ready for use in ComfyUI.")
        return True
    else:
        print("\nERROR: Some tests failed.")
        print("Please check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
