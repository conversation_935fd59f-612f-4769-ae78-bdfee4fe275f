# Maya Bridge for ComfyUI

A powerful plugin that bridges Maya and ComfyUI, allowing you to use ComfyUI as a renderer for Maya scenes with real-time pose and depth data extraction.

## Features

### 🎭 Maya Integration
- **Real-time connection** to Maya via socket communication
- **Bone position extraction** from Maya rigs
- **Viewport image capture** from <PERSON>'s active camera
- **Depth buffer extraction** from Maya scenes
- **Camera information** including matrices and parameters

### 🦴 Advanced Bone Mapping
- **Multiple rig presets**: Standard, Mixamo, HumanIK, Advanced Skeleton, MHX2
- **Custom bone mapping** with JSON configuration
- **Auto-detection** of bone names from Maya scenes
- **OpenPose COCO 18 keypoints** compatibility
- **Missing bone handling** with graceful fallbacks

### 🎯 Pose Processing
- **Character facing detection** (front/back) with multiple heuristics
- **Pose smoothing** for stable animations
- **Horizontal flipping** for character orientation
- **Real-time pose visualization** with customizable rendering

### 🌊 Depth Processing
- **Maya depth buffer** direct extraction
- **DepthAnythingV2** fallback generation
- **Multiple depth sources** (Maya buffer, generated, auto)
- **Configurable depth range** and normalization

### ⚡ Performance Features
- **Real-time mode** with configurable frame rates
- **Caching system** for improved performance
- **Batch processing** support
- **GPU acceleration** when available

## Installation

### Automatic Installation

1. Download the Maya Bridge plugin files
2. Run the installation script:
   ```bash
   python install_maya_bridge.py
   ```
3. Follow the on-screen instructions

### Manual Installation

#### ComfyUI Setup
1. Copy the plugin files to your ComfyUI custom_nodes directory:
   ```
   ComfyUI/custom_nodes/comfyui_maya_bridge/
   ├── node_wrappers/
   │   ├── maya_bridge.py
   │   └── maya_bridge_advanced.py
   ├── src/
   │   ├── maya_connector.py
   │   ├── bone_mapping_presets.py
   │   └── maya_bridge_config.py
   └── __init__.py
   ```

#### Maya Setup
1. Copy Maya scripts to your Maya scripts directory:
   ```
   ~/maya/[version]/scripts/
   ├── comfyui_bridge_server.py
   ├── depth_buffer_extractor.py
   └── comfyui_bridge_shelf.py
   ```

2. In Maya, run the shelf creation script:
   ```python
   exec(open('comfyui_bridge_shelf.py').read())
   ```

## Usage

### Basic Workflow

1. **Start Maya Bridge Server**
   - In Maya, click the "Start Bridge" button on the ComfyUI Bridge shelf
   - Or run in Maya's Script Editor: `start_comfyui_bridge()`

2. **Add Maya Bridge Node in ComfyUI**
   - Find "Maya Bridge (Pose + Depth)" in ControlNet Preprocessors/Maya Bridge
   - Connect it to your workflow

3. **Configure Connection**
   - Set Maya host (default: localhost)
   - Set Maya port (default: 7001)
   - Choose bone mapping preset or use custom mapping

4. **Process Scene Data**
   - The node will automatically connect to Maya
   - Extract pose and depth information
   - Generate OpenPose-compatible output

### Advanced Features

#### Custom Bone Mapping
Create custom bone mappings in JSON format:
```json
{
  "head": 0,
  "neck": 1,
  "rightShoulder": 2,
  "rightElbow": 3,
  "rightWrist": 4,
  "leftShoulder": 5,
  "leftElbow": 6,
  "leftWrist": 7,
  "rightHip": 8,
  "rightKnee": 9,
  "rightAnkle": 10,
  "leftHip": 11,
  "leftKnee": 12,
  "leftAnkle": 13,
  "rightEye": 14,
  "leftEye": 15,
  "rightEar": 16,
  "leftEar": 17
}
```

#### Real-time Mode
Enable real-time mode for live updates:
- Set "Real Time Mode" to "enable"
- Configure frame rate (1-60 FPS)
- Use trigger capture for manual updates

#### Bone Mapping Presets

| Preset | Description | Use Case |
|--------|-------------|----------|
| **Standard** | Generic Maya joint names | Custom rigs, general use |
| **Mixamo** | Adobe Mixamo character rigs | Mixamo imported characters |
| **HumanIK** | Maya HumanIK system | HumanIK rigged characters |
| **Advanced Skeleton** | Advanced Skeleton plugin | Advanced Skeleton rigs |
| **MHX2** | MakeHuman MHX2 format | MakeHuman characters |

## Configuration

### Connection Settings
- **Maya Host**: IP address of Maya machine (default: localhost)
- **Maya Port**: Communication port (default: 7001)
- **Connection Timeout**: Timeout in seconds (default: 5.0)

### Bone Mapping Settings
- **Bone Mapping Preset**: Choose from available presets
- **Auto Detect Bones**: Automatically match bone names
- **Ignore Missing Bones**: Continue processing with missing bones
- **Custom Bone Mapping**: JSON configuration for custom rigs

### Pose Processing Settings
- **Character Facing**: Auto-detect or manually set (front/back)
- **Flip Horizontal**: Mirror the pose horizontally
- **Pose Smoothing**: Apply temporal smoothing for animations
- **Smoothing Factor**: Smoothing strength (0.1-0.9)

### Depth Processing Settings
- **Enable Depth**: Generate depth information
- **Depth Source**: Maya buffer, generated, or auto
- **Depth Model**: DepthAnythingV2 model variant
- **Max Depth**: Maximum depth value for normalization

## Troubleshooting

### Connection Issues
- **"Failed to connect to Maya"**
  - Ensure Maya Bridge Server is running
  - Check firewall settings for port 7001
  - Verify Maya and ComfyUI are on the same network

### Bone Mapping Issues
- **"Missing bone: [bone_name]"**
  - Enable "Ignore Missing Bones"
  - Use "Auto Detect Bones" feature
  - Create custom bone mapping for your rig

### Performance Issues
- **Slow processing**
  - Enable caching in performance settings
  - Reduce resolution for faster processing
  - Use GPU acceleration if available

### Maya Script Errors
- **"Module not found"**
  - Ensure Maya scripts are in the correct directory
  - Check Maya's script path settings
  - Restart Maya after installing scripts

## API Reference

### Maya Bridge Node Inputs

| Input | Type | Default | Description |
|-------|------|---------|-------------|
| maya_host | STRING | "localhost" | Maya server hostname |
| maya_port | INT | 7001 | Maya server port |
| bone_mapping_preset | COMBO | "standard" | Bone mapping preset |
| custom_bone_mapping | STRING | "{}" | Custom bone mapping JSON |
| character_facing | COMBO | "auto" | Character orientation |
| enable_depth | COMBO | "enable" | Enable depth processing |
| resolution | INT | 512 | Output resolution |

### Maya Bridge Node Outputs

| Output | Type | Description |
|--------|------|-------------|
| pose_image | IMAGE | Pose visualization image |
| depth_image | IMAGE | Depth map image |
| pose_keypoints | POSE_KEYPOINT | OpenPose keypoints data |
| status_info | STRING | Processing status and debug info |

## Examples

### Basic Pose Extraction
```python
# Maya Bridge node configuration
maya_host = "localhost"
maya_port = 7001
bone_mapping_preset = "mixamo"
character_facing = "auto"
resolution = 512
```

### Custom Rig Setup
```python
# Custom bone mapping for specific rig
custom_bone_mapping = {
    "Character1_Head": 0,
    "Character1_Neck": 1,
    "Character1_R_Shoulder": 2,
    # ... more mappings
}
```

### Real-time Animation
```python
# Real-time mode configuration
real_time_mode = "enable"
frame_rate = 30
pose_smoothing = "enable"
smoothing_factor = 0.3
```

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

### Development Setup
1. Clone the repository
2. Install development dependencies
3. Run tests with `python -m pytest`
4. Follow the coding standards

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **OpenPose** for the pose estimation format
- **DepthAnything** for depth estimation
- **ComfyUI** for the extensible node system
- **Maya** for the 3D animation platform

## Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Join the ComfyUI community discussions

---

**Maya Bridge Plugin** - Bridging the gap between Maya and ComfyUI for next-generation 3D workflows.
