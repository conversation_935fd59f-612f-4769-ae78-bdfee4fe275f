"""
Configuration management for Maya Bridge plugin
"""

import json
import os
from typing import Dict, Any, Optional
from pathlib import Path

class MayaBridgeConfig:
    """Configuration manager for Maya Bridge plugin"""
    
    DEFAULT_CONFIG = {
        "maya_connection": {
            "default_host": "localhost",
            "default_port": 7001,
            "timeout": 5.0,
            "retry_attempts": 3,
            "retry_delay": 1.0
        },
        "bone_mapping": {
            "default_preset": "standard",
            "auto_detect": True,
            "ignore_missing": True,
            "confidence_threshold": 0.5
        },
        "pose_processing": {
            "auto_facing_detection": True,
            "facing_confidence_threshold": 2.0,
            "limb_occlusion_threshold": 0.7,
            "keypoint_smoothing": False,
            "smoothing_factor": 0.3
        },
        "depth_processing": {
            "default_model": "depth_anything_v2_vitl.pth",
            "max_depth": 20.0,
            "depth_normalization": "linear",
            "use_maya_depth_buffer": True,
            "fallback_to_generated": True
        },
        "rendering": {
            "default_resolution": 512,
            "pose_line_thickness": 2,
            "keypoint_radius": 4,
            "pose_colors": "rainbow",
            "background_color": [0, 0, 0]
        },
        "performance": {
            "cache_models": True,
            "max_cache_size": 3,
            "use_gpu_if_available": True,
            "batch_processing": False
        },
        "debug": {
            "verbose_logging": False,
            "save_intermediate_results": False,
            "debug_output_dir": "debug_output"
        }
    }
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or self._get_default_config_path()
        self.config = self.DEFAULT_CONFIG.copy()
        self.load_config()
    
    def _get_default_config_path(self) -> str:
        """Get default configuration file path"""
        # Try to use ComfyUI's custom_nodes directory
        try:
            current_dir = Path(__file__).parent.parent
            config_dir = current_dir / "config"
            config_dir.mkdir(exist_ok=True)
            return str(config_dir / "maya_bridge_config.json")
        except:
            # Fallback to current directory
            return "maya_bridge_config.json"
    
    def load_config(self) -> bool:
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    user_config = json.load(f)
                    self._merge_config(self.config, user_config)
                return True
        except Exception as e:
            print(f"Failed to load config: {e}")
        return False
    
    def save_config(self) -> bool:
        """Save configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            return True
        except Exception as e:
            print(f"Failed to save config: {e}")
            return False
    
    def _merge_config(self, base: Dict, update: Dict):
        """Recursively merge configuration dictionaries"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (e.g., 'maya_connection.default_host')"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config = self.config
        
        try:
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            config[keys[-1]] = value
            return True
        except Exception:
            return False
    
    def get_maya_connection_config(self) -> Dict[str, Any]:
        """Get Maya connection configuration"""
        return self.config.get("maya_connection", {})
    
    def get_bone_mapping_config(self) -> Dict[str, Any]:
        """Get bone mapping configuration"""
        return self.config.get("bone_mapping", {})
    
    def get_pose_processing_config(self) -> Dict[str, Any]:
        """Get pose processing configuration"""
        return self.config.get("pose_processing", {})
    
    def get_depth_processing_config(self) -> Dict[str, Any]:
        """Get depth processing configuration"""
        return self.config.get("depth_processing", {})
    
    def get_rendering_config(self) -> Dict[str, Any]:
        """Get rendering configuration"""
        return self.config.get("rendering", {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance configuration"""
        return self.config.get("performance", {})
    
    def get_debug_config(self) -> Dict[str, Any]:
        """Get debug configuration"""
        return self.config.get("debug", {})
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        self.config = self.DEFAULT_CONFIG.copy()
    
    def export_config(self, file_path: str) -> bool:
        """Export configuration to specified file"""
        try:
            with open(file_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            return True
        except Exception:
            return False
    
    def import_config(self, file_path: str) -> bool:
        """Import configuration from specified file"""
        try:
            with open(file_path, 'r') as f:
                imported_config = json.load(f)
                self._merge_config(self.config, imported_config)
            return True
        except Exception:
            return False
    
    def validate_config(self) -> tuple[bool, list[str]]:
        """Validate current configuration"""
        errors = []
        
        # Validate Maya connection settings
        maya_config = self.get_maya_connection_config()
        if not isinstance(maya_config.get("default_port"), int) or not (1000 <= maya_config.get("default_port", 0) <= 65535):
            errors.append("Invalid Maya port number")
        
        if not isinstance(maya_config.get("timeout"), (int, float)) or maya_config.get("timeout", 0) <= 0:
            errors.append("Invalid timeout value")
        
        # Validate depth processing settings
        depth_config = self.get_depth_processing_config()
        if not isinstance(depth_config.get("max_depth"), (int, float)) or depth_config.get("max_depth", 0) <= 0:
            errors.append("Invalid max_depth value")
        
        # Validate rendering settings
        render_config = self.get_rendering_config()
        if not isinstance(render_config.get("default_resolution"), int) or render_config.get("default_resolution", 0) <= 0:
            errors.append("Invalid default_resolution value")
        
        return len(errors) == 0, errors
    
    def get_config_summary(self) -> str:
        """Get a summary of current configuration"""
        maya_config = self.get_maya_connection_config()
        bone_config = self.get_bone_mapping_config()
        depth_config = self.get_depth_processing_config()
        
        summary = f"""Maya Bridge Configuration Summary:
        
Maya Connection:
  - Host: {maya_config.get('default_host', 'localhost')}
  - Port: {maya_config.get('default_port', 7001)}
  - Timeout: {maya_config.get('timeout', 5.0)}s

Bone Mapping:
  - Default Preset: {bone_config.get('default_preset', 'standard')}
  - Auto Detect: {bone_config.get('auto_detect', True)}
  - Ignore Missing: {bone_config.get('ignore_missing', True)}

Depth Processing:
  - Model: {depth_config.get('default_model', 'depth_anything_v2_vitl.pth')}
  - Max Depth: {depth_config.get('max_depth', 20.0)}
  - Use Maya Buffer: {depth_config.get('use_maya_depth_buffer', True)}

Config File: {self.config_file}
"""
        return summary

# Global configuration instance
_config_instance = None

def get_maya_bridge_config(config_file: Optional[str] = None) -> MayaBridgeConfig:
    """Get global Maya Bridge configuration instance"""
    global _config_instance
    
    if _config_instance is None or (config_file and config_file != _config_instance.config_file):
        _config_instance = MayaBridgeConfig(config_file)
    
    return _config_instance

def reset_maya_bridge_config():
    """Reset global configuration instance"""
    global _config_instance
    _config_instance = None
