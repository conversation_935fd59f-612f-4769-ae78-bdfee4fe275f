"""
Maya Bridge Preprocessor for ComfyUI
Combines DWPose and DepthAnythingV2 functionality to bridge Maya with ComfyUI
"""

from ..utils import common_annotator_call, INPUT, define_preprocessor_inputs
import comfy.model_management as model_management
import numpy as np
import json
import cv2
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image

# OpenPose COCO 18 keypoints definition
OPENPOSE_KEYPOINTS = {
    0: "Nose",
    1: "Neck", 
    2: "RShoulder",
    3: "RE<PERSON><PERSON>",
    4: "RWrist",
    5: "LShoulder", 
    6: "LEl<PERSON>",
    7: "L<PERSON>rist",
    8: "RHip",
    9: "RKnee", 
    10: "RAnkle",
    11: "LHip",
    12: "LKnee",
    13: "LAnkle",
    14: "REye",
    15: "LEye",
    16: "REar",
    17: "LEar"
}

# Default Maya to OpenPose bone mapping
DEFAULT_MAYA_BONE_MAPPING = {
    "head": 0,  # Nose
    "neck": 1,  # Neck
    "rightShoulder": 2,  # RShoulder
    "rightElbow": 3,  # RE<PERSON>bow
    "rightWrist": 4,  # RWrist
    "leftShoulder": 5,  # LShoulder
    "leftElbow": 6,  # LElbow
    "leftWrist": 7,  # LWrist
    "rightHip": 8,  # RHip
    "rightKnee": 9,  # RKnee
    "rightAnkle": 10,  # RAnkle
    "leftHip": 11,  # LHip
    "leftKnee": 12,  # LKnee
    "leftAnkle": 13,  # LAnkle
    "rightEye": 14,  # REye
    "leftEye": 15,  # LEye
    "rightEar": 16,  # REar
    "leftEar": 17   # LEar
}

class MayaBridgePreprocessor:
    @classmethod
    def INPUT_TYPES(s):
        # Import bone mapping presets
        try:
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
            from bone_mapping_presets import get_bone_mapping_manager

            manager = get_bone_mapping_manager()
            preset_names = ["custom"] + manager.get_preset_names()
            default_mapping = manager.get_preset("standard")["mapping"] if manager.get_preset("standard") else DEFAULT_MAYA_BONE_MAPPING
        except:
            preset_names = ["custom", "standard", "mixamo", "humanik"]
            default_mapping = DEFAULT_MAYA_BONE_MAPPING

        return define_preprocessor_inputs(
            # Maya connection settings
            maya_host=INPUT.STRING(default="localhost"),
            maya_port=INPUT.INT(default=7001, min=1000, max=65535),

            # Bone mapping settings
            bone_mapping_preset=INPUT.COMBO(preset_names, default="standard"),
            custom_bone_mapping=INPUT.STRING(default=json.dumps(default_mapping, indent=2), multiline=True),
            ignore_missing_bones=INPUT.COMBO(["enable", "disable"], default="enable"),
            auto_detect_bones=INPUT.COMBO(["disable", "enable"], default="enable"),

            # Character orientation
            character_facing=INPUT.COMBO(["auto", "front", "back"], default="auto"),
            flip_horizontal=INPUT.COMBO(["disable", "enable"], default="disable"),

            # Pose detection settings
            detect_hands=INPUT.COMBO(["disable", "enable"], default="disable"),
            detect_face=INPUT.COMBO(["disable", "enable"], default="disable"),

            # Depth settings
            enable_depth=INPUT.COMBO(["enable", "disable"], default="enable"),
            depth_model=INPUT.COMBO([
                "depth_anything_v2_vitl.pth",
                "depth_anything_v2_vitb.pth",
                "depth_anything_v2_vits.pth"
            ], default="depth_anything_v2_vitl.pth"),
            max_depth=INPUT.FLOAT(default=20.0, min=0.1, max=100.0, step=0.1),

            # Output settings
            resolution=INPUT.RESOLUTION(),
            output_format=INPUT.COMBO(["pose_only", "depth_only", "combined"], default="combined")
        )

    RETURN_TYPES = ("IMAGE", "IMAGE", "POSE_KEYPOINT", "STRING")
    RETURN_NAMES = ("pose_image", "depth_image", "pose_keypoints", "debug_info")
    FUNCTION = "process_maya_data"
    CATEGORY = "ControlNet Preprocessors/Maya Bridge"

    def __init__(self):
        self.maya_connector = None
        self.pose_detector = None
        self.depth_detector = None

    def process_maya_data(self, image, maya_host="localhost", maya_port=7001,
                         bone_mapping_preset="standard", custom_bone_mapping=None,
                         ignore_missing_bones="enable", auto_detect_bones="enable",
                         character_facing="auto", flip_horizontal="disable",
                         detect_hands="disable", detect_face="disable",
                         enable_depth="enable", depth_model="depth_anything_v2_vitl.pth",
                         max_depth=20.0, resolution=512, output_format="combined", **kwargs):
        
        try:
            # Get bone mapping
            bone_map = self._get_bone_mapping(bone_mapping_preset, custom_bone_mapping)
            debug_info = []
            debug_info.append(f"Using bone mapping preset: {bone_mapping_preset}")
            
            # Initialize Maya connector
            maya_data = self._get_maya_data(maya_host, maya_port)
            if not maya_data:
                debug_info.append("Failed to connect to Maya")
                return self._create_fallback_output(image, resolution, debug_info)
            
            debug_info.append(f"Connected to Maya at {maya_host}:{maya_port}")
            
            # Auto-detect bones if enabled
            if auto_detect_bones == "enable" and maya_data.get("bone_positions"):
                bone_map = self._auto_detect_bone_mapping(
                    list(maya_data["bone_positions"].keys()),
                    bone_map, debug_info
                )

            # Process pose data
            pose_image, pose_keypoints = self._process_pose_data(
                maya_data, bone_map, ignore_missing_bones == "enable",
                character_facing, flip_horizontal == "enable",
                detect_hands == "enable", detect_face == "enable",
                resolution, debug_info
            )
            
            # Process depth data
            depth_image = None
            if enable_depth == "enable":
                depth_image = self._process_depth_data(
                    maya_data, depth_model, max_depth, resolution, debug_info
                )
            
            # Return based on output format
            if output_format == "pose_only":
                return (pose_image, pose_image, pose_keypoints, "\n".join(debug_info))
            elif output_format == "depth_only" and depth_image is not None:
                return (depth_image, depth_image, pose_keypoints, "\n".join(debug_info))
            else:
                # Combined output
                if depth_image is None:
                    depth_image = pose_image
                return (pose_image, depth_image, pose_keypoints, "\n".join(debug_info))
                
        except Exception as e:
            debug_info.append(f"Error: {str(e)}")
            return self._create_fallback_output(image, resolution, debug_info)

    def _get_bone_mapping(self, preset_name: str, custom_mapping: str) -> Dict[str, int]:
        """Get bone mapping based on preset or custom configuration"""
        try:
            # Import bone mapping manager
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
            from bone_mapping_presets import get_bone_mapping_manager

            manager = get_bone_mapping_manager()

            if preset_name == "custom" and custom_mapping:
                # Use custom mapping
                try:
                    return json.loads(custom_mapping)
                except json.JSONDecodeError:
                    # Fall back to default if custom mapping is invalid
                    preset = manager.get_preset("standard")
                    return preset["mapping"] if preset else DEFAULT_MAYA_BONE_MAPPING
            else:
                # Use preset
                preset = manager.get_preset(preset_name)
                return preset["mapping"] if preset else DEFAULT_MAYA_BONE_MAPPING

        except Exception:
            return DEFAULT_MAYA_BONE_MAPPING

    def _auto_detect_bone_mapping(self, available_bones: List[str],
                                 current_mapping: Dict[str, int],
                                 debug_info: List[str]) -> Dict[str, int]:
        """Auto-detect bone mapping based on available Maya bones"""
        try:
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
            from bone_mapping_presets import get_bone_mapping_manager

            manager = get_bone_mapping_manager()
            suggestions = manager.suggest_bone_names(available_bones, "standard")

            updated_mapping = current_mapping.copy()
            detected_count = 0

            # Try to match available bones to OpenPose keypoints
            for openpose_name, bone_candidates in suggestions.items():
                if bone_candidates:
                    # Find the OpenPose index for this keypoint
                    openpose_info = manager.get_all_openpose_info()
                    openpose_idx = None

                    for idx, info in openpose_info.items():
                        if info["name"] == openpose_name:
                            openpose_idx = idx
                            break

                    if openpose_idx is not None:
                        # Use the first matching bone
                        best_match = bone_candidates[0]
                        updated_mapping[best_match] = openpose_idx
                        detected_count += 1

            debug_info.append(f"Auto-detected {detected_count} bone mappings")
            return updated_mapping

        except Exception as e:
            debug_info.append(f"Auto-detection failed: {str(e)}")
            return current_mapping

    def _get_maya_data(self, host: str, port: int) -> Optional[Dict]:
        """Connect to Maya and get current scene data"""
        try:
            # Import Maya connector
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
            from maya_connector import get_maya_connector

            # Get Maya connector
            connector = get_maya_connector(host, port)

            # Get scene data
            scene_data = connector.get_scene_data()

            if scene_data:
                return scene_data
            else:
                # Return mock data for testing if Maya connection fails
                return {
                    "viewport_image": None,
                    "bone_positions": self._get_mock_bone_positions(),
                    "camera_info": {},
                    "depth_buffer": None
                }
        except Exception as e:
            print(f"Maya connection error: {e}")
            return None

    def _get_mock_bone_positions(self) -> Dict[str, Tuple[float, float, float]]:
        """Generate mock bone positions for testing"""
        return {
            "head": (0.0, 8.0, 0.0),
            "neck": (0.0, 7.0, 0.0),
            "rightShoulder": (2.0, 6.5, 0.0),
            "rightElbow": (3.5, 5.0, 0.0),
            "rightWrist": (4.5, 3.5, 0.0),
            "leftShoulder": (-2.0, 6.5, 0.0),
            "leftElbow": (-3.5, 5.0, 0.0),
            "leftWrist": (-4.5, 3.5, 0.0),
            "rightHip": (1.0, 3.0, 0.0),
            "rightKnee": (1.0, 1.5, 0.0),
            "rightAnkle": (1.0, 0.0, 0.0),
            "leftHip": (-1.0, 3.0, 0.0),
            "leftKnee": (-1.0, 1.5, 0.0),
            "leftAnkle": (-1.0, 0.0, 0.0),
            "rightEye": (0.5, 8.2, 0.5),
            "leftEye": (-0.5, 8.2, 0.5),
            "rightEar": (1.0, 8.0, 0.0),
            "leftEar": (-1.0, 8.0, 0.0)
        }

    def _process_pose_data(self, maya_data: Dict, bone_map: Dict, 
                          ignore_missing: bool, facing: str, flip_h: bool,
                          detect_hands: bool, detect_face: bool, 
                          resolution: int, debug_info: List[str]) -> Tuple[Any, Any]:
        """Process Maya bone data into OpenPose format"""
        
        # Create OpenPose keypoints from Maya bone data
        keypoints = self._map_maya_bones_to_openpose(
            maya_data.get("bone_positions", {}), 
            bone_map, ignore_missing, debug_info
        )
        
        # Determine character facing direction
        if facing == "auto":
            facing_direction = self._detect_character_facing(keypoints)
            debug_info.append(f"Auto-detected facing: {facing_direction}")
        else:
            facing_direction = facing
        
        # Apply horizontal flip if needed
        if flip_h or facing_direction == "back":
            keypoints = self._flip_keypoints_horizontal(keypoints)
            debug_info.append("Applied horizontal flip")
        
        # Generate pose image
        pose_image = self._generate_pose_image(keypoints, resolution, detect_hands, detect_face)
        
        # Create pose keypoints in OpenPose JSON format
        pose_json = self._create_openpose_json(keypoints, resolution, resolution)
        
        return pose_image, pose_json

    def _process_depth_data(self, maya_data: Dict, model_name: str, 
                           max_depth: float, resolution: int, 
                           debug_info: List[str]) -> Optional[Any]:
        """Process Maya depth buffer or generate depth from viewport image"""
        
        try:
            from custom_controlnet_aux.depth_anything_v2 import DepthAnythingV2Detector
            
            # Initialize depth detector if needed
            if self.depth_detector is None:
                self.depth_detector = DepthAnythingV2Detector.from_pretrained(
                    filename=model_name
                ).to(model_management.get_torch_device())
            
            # Get depth data from Maya or generate from image
            depth_buffer = maya_data.get("depth_buffer")
            if depth_buffer is not None:
                # Use Maya's depth buffer directly
                depth_image = self._process_maya_depth_buffer(depth_buffer, max_depth, resolution)
                debug_info.append("Used Maya depth buffer")
            else:
                # Generate depth from viewport image using DepthAnything
                viewport_image = maya_data.get("viewport_image")
                if viewport_image is not None:
                    depth_image = self.depth_detector(viewport_image, 
                                                    detect_resolution=resolution, 
                                                    max_depth=max_depth)
                    debug_info.append("Generated depth using DepthAnythingV2")
                else:
                    debug_info.append("No depth data or viewport image available")
                    return None
            
            return depth_image
            
        except Exception as e:
            debug_info.append(f"Depth processing error: {str(e)}")
            return None

    def _map_maya_bones_to_openpose(self, maya_bones: Dict, bone_map: Dict, 
                                   ignore_missing: bool, debug_info: List[str]) -> List[Optional[Tuple[float, float]]]:
        """Map Maya bone positions to OpenPose keypoints"""
        
        keypoints = [None] * 18  # Initialize 18 OpenPose keypoints
        mapped_count = 0
        
        for maya_bone, openpose_idx in bone_map.items():
            if maya_bone in maya_bones and 0 <= openpose_idx < 18:
                bone_pos = maya_bones[maya_bone]
                # Convert 3D Maya position to 2D screen coordinates
                screen_pos = self._world_to_screen(bone_pos)
                keypoints[openpose_idx] = screen_pos
                mapped_count += 1
            elif not ignore_missing:
                debug_info.append(f"Missing bone: {maya_bone}")
        
        debug_info.append(f"Mapped {mapped_count}/{len(bone_map)} bones")
        return keypoints

    def _world_to_screen(self, world_pos: Tuple[float, float, float]) -> Tuple[float, float]:
        """Convert 3D world position to 2D screen coordinates"""
        # This is a simplified conversion - in practice, you'd use Maya's camera matrix
        x, y, z = world_pos
        # Simple orthographic projection for now
        screen_x = (x + 10) / 20  # Normalize to 0-1 range
        screen_y = (y + 10) / 20  # Normalize to 0-1 range
        return (max(0, min(1, screen_x)), max(0, min(1, screen_y)))

    def _detect_character_facing(self, keypoints: List) -> str:
        """Auto-detect if character is facing front or back using multiple heuristics"""

        # Heuristic 1: Shoulder position comparison
        left_shoulder = keypoints[5]  # LShoulder
        right_shoulder = keypoints[2]  # RShoulder
        shoulder_score = 0

        if left_shoulder and right_shoulder:
            # If left shoulder appears to the right of right shoulder, likely facing back
            if left_shoulder[0] > right_shoulder[0]:
                shoulder_score += 1

        # Heuristic 2: Hip position comparison
        left_hip = keypoints[11]  # LHip
        right_hip = keypoints[8]   # RHip
        hip_score = 0

        if left_hip and right_hip:
            # Similar logic for hips
            if left_hip[0] > right_hip[0]:
                hip_score += 1

        # Heuristic 3: Eye/Ear visibility
        left_eye = keypoints[15]   # LEye
        right_eye = keypoints[14]  # REye
        left_ear = keypoints[17]   # LEar
        right_ear = keypoints[16]  # REar
        face_score = 0

        # Count visible facial features
        visible_features = sum([1 for feature in [left_eye, right_eye, left_ear, right_ear] if feature])

        # If fewer facial features are visible, might be facing away
        if visible_features < 2:
            face_score += 1

        # Heuristic 4: Limb occlusion patterns
        limb_score = 0
        limbs = [
            (keypoints[2], keypoints[3], keypoints[4]),  # Right arm
            (keypoints[5], keypoints[6], keypoints[7]),  # Left arm
            (keypoints[8], keypoints[9], keypoints[10]), # Right leg
            (keypoints[11], keypoints[12], keypoints[13]) # Left leg
        ]

        # Count complete limbs (all joints visible)
        complete_limbs = sum([1 for limb in limbs if all(joint for joint in limb)])

        # If fewer complete limbs, might indicate occlusion from back view
        if complete_limbs < 3:
            limb_score += 0.5

        # Combine scores
        total_score = shoulder_score + hip_score + face_score + limb_score

        # Decision threshold
        if total_score >= 2:
            return "back"
        else:
            return "front"

    def _flip_keypoints_horizontal(self, keypoints: List) -> List:
        """Flip keypoints horizontally"""
        flipped = keypoints.copy()
        
        # Flip x coordinates
        for i, kp in enumerate(flipped):
            if kp:
                flipped[i] = (1.0 - kp[0], kp[1])
        
        # Swap left/right keypoints
        swap_pairs = [(2, 5), (3, 6), (4, 7), (8, 11), (9, 12), (10, 13), (14, 15), (16, 17)]
        for left_idx, right_idx in swap_pairs:
            flipped[left_idx], flipped[right_idx] = flipped[right_idx], flipped[left_idx]
        
        return flipped

    def _generate_pose_image(self, keypoints: List, resolution: int, 
                           detect_hands: bool, detect_face: bool) -> Any:
        """Generate pose visualization image"""
        
        # Create blank canvas
        canvas = np.zeros((resolution, resolution, 3), dtype=np.uint8)
        
        # Draw pose skeleton
        canvas = self._draw_pose_skeleton(canvas, keypoints)
        
        # Convert to PIL Image
        return Image.fromarray(canvas)

    def _draw_pose_skeleton(self, canvas: np.ndarray, keypoints: List) -> np.ndarray:
        """Draw pose skeleton on canvas"""
        
        H, W = canvas.shape[:2]
        
        # Define limb connections (OpenPose COCO format)
        limb_seq = [
            [1, 2], [1, 5], [2, 3], [3, 4], [5, 6], [6, 7],
            [1, 8], [8, 9], [9, 10], [1, 11], [11, 12], [12, 13],
            [1, 0], [0, 14], [14, 16], [0, 15], [15, 17]
        ]
        
        colors = [
            [255, 0, 0], [255, 85, 0], [255, 170, 0], [255, 255, 0], [170, 255, 0],
            [85, 255, 0], [0, 255, 0], [0, 255, 85], [0, 255, 170], [0, 255, 255],
            [0, 170, 255], [0, 85, 255], [0, 0, 255], [85, 0, 255], [170, 0, 255],
            [255, 0, 255], [255, 0, 170]
        ]
        
        # Draw limbs
        for i, (k1_idx, k2_idx) in enumerate(limb_seq):
            if k1_idx < len(keypoints) and k2_idx < len(keypoints):
                kp1 = keypoints[k1_idx]
                kp2 = keypoints[k2_idx]
                
                if kp1 and kp2:
                    x1, y1 = int(kp1[0] * W), int(kp1[1] * H)
                    x2, y2 = int(kp2[0] * W), int(kp2[1] * H)
                    
                    color = colors[i % len(colors)]
                    cv2.line(canvas, (x1, y1), (x2, y2), color, 2)
        
        # Draw keypoints
        for kp in keypoints:
            if kp:
                x, y = int(kp[0] * W), int(kp[1] * H)
                cv2.circle(canvas, (x, y), 4, (0, 255, 0), -1)
        
        return canvas

    def _create_openpose_json(self, keypoints: List, height: int, width: int) -> Dict:
        """Create OpenPose JSON format output"""
        
        # Convert keypoints to OpenPose format [x, y, confidence]
        pose_keypoints_2d = []
        for kp in keypoints:
            if kp:
                pose_keypoints_2d.extend([kp[0] * width, kp[1] * height, 1.0])
            else:
                pose_keypoints_2d.extend([0.0, 0.0, 0.0])
        
        return {
            "people": [{
                "pose_keypoints_2d": pose_keypoints_2d,
                "face_keypoints_2d": [],
                "hand_left_keypoints_2d": [],
                "hand_right_keypoints_2d": []
            }],
            "canvas_height": height,
            "canvas_width": width
        }

    def _process_maya_depth_buffer(self, depth_buffer: np.ndarray, 
                                  max_depth: float, resolution: int) -> Any:
        """Process Maya's depth buffer into depth image"""
        
        # Normalize depth values
        depth_normalized = np.clip(depth_buffer / max_depth, 0, 1)
        depth_image = (depth_normalized * 255).astype(np.uint8)
        
        # Resize to target resolution
        depth_image = cv2.resize(depth_image, (resolution, resolution))
        
        # Convert to 3-channel image
        depth_image = np.stack([depth_image] * 3, axis=-1)
        
        return Image.fromarray(depth_image)

    def _create_fallback_output(self, image: Any, resolution: int, 
                               debug_info: List[str]) -> Tuple:
        """Create fallback output when Maya connection fails"""
        
        # Create empty pose image
        empty_canvas = np.zeros((resolution, resolution, 3), dtype=np.uint8)
        empty_image = Image.fromarray(empty_canvas)
        
        # Create empty pose keypoints
        empty_keypoints = {
            "people": [],
            "canvas_height": resolution,
            "canvas_width": resolution
        }
        
        return (empty_image, empty_image, empty_keypoints, "\n".join(debug_info))


NODE_CLASS_MAPPINGS = {
    "MayaBridgePreprocessor": MayaBridgePreprocessor
}

NODE_DISPLAY_NAME_MAPPINGS = {
    "MayaBridgePreprocessor": "Maya Bridge (Pose + Depth)"
}
