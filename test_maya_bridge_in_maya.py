"""
Maya Bridge 测试脚本 - 在Maya中运行
"""
import maya.cmds as cmds
import maya.standalone
import sys
import os
import socket
import json
import struct
import threading
import time

def initialize_maya():
    """初始化Maya standalone"""
    try:
        maya.standalone.initialize()
        print("✅ Maya standalone 初始化成功")
        return True
    except Exception as e:
        print(f"❌ Maya standalone 初始化失败: {e}")
        return False

def create_test_scene():
    """创建测试场景"""
    try:
        # 创建新场景
        cmds.file(new=True, force=True)
        
        # 创建一个简单的骨骼链
        joints = []
        
        # 创建根关节
        root_joint = cmds.joint(name='root', position=[0, 0, 0])
        joints.append(root_joint)
        
        # 创建脊椎关节
        spine_joint = cmds.joint(name='spine', position=[0, 5, 0])
        joints.append(spine_joint)
        
        # 创建头部关节
        head_joint = cmds.joint(name='head', position=[0, 8, 0])
        joints.append(head_joint)
        
        # 选择根关节并创建左臂
        cmds.select(spine_joint)
        left_shoulder = cmds.joint(name='leftShoulder', position=[-2, 7, 0])
        left_elbow = cmds.joint(name='leftElbow', position=[-4, 5, 0])
        left_wrist = cmds.joint(name='leftWrist', position=[-6, 3, 0])
        joints.extend([left_shoulder, left_elbow, left_wrist])
        
        # 创建右臂
        cmds.select(spine_joint)
        right_shoulder = cmds.joint(name='rightShoulder', position=[2, 7, 0])
        right_elbow = cmds.joint(name='rightElbow', position=[4, 5, 0])
        right_wrist = cmds.joint(name='rightWrist', position=[6, 3, 0])
        joints.extend([right_shoulder, right_elbow, right_wrist])
        
        # 创建左腿
        cmds.select(root_joint)
        left_hip = cmds.joint(name='leftHip', position=[-1, 0, 0])
        left_knee = cmds.joint(name='leftKnee', position=[-1, -3, 0])
        left_ankle = cmds.joint(name='leftAnkle', position=[-1, -6, 0])
        joints.extend([left_hip, left_knee, left_ankle])
        
        # 创建右腿
        cmds.select(root_joint)
        right_hip = cmds.joint(name='rightHip', position=[1, 0, 0])
        right_knee = cmds.joint(name='rightKnee', position=[1, -3, 0])
        right_ankle = cmds.joint(name='rightAnkle', position=[1, -6, 0])
        joints.extend([right_hip, right_knee, right_ankle])
        
        print(f"✅ 创建了测试场景，包含 {len(joints)} 个关节")
        return joints
        
    except Exception as e:
        print(f"❌ 创建测试场景失败: {e}")
        return []

class SimpleMayaBridge:
    """简化的Maya Bridge服务器"""
    
    def __init__(self, port=7001):
        self.port = port
        self.running = False
        self.server_socket = None
        self.server_thread = None
    
    def start(self):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', self.port))
            self.server_socket.listen(5)
            
            self.running = True
            self.server_thread = threading.Thread(target=self._server_loop)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            print(f"✅ Maya Bridge Server 启动成功，端口: {self.port}")
            return True
            
        except Exception as e:
            print(f"❌ 启动服务器失败: {e}")
            return False
    
    def stop(self):
        """停止服务器"""
        self.running = False
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        print("⏹️ Maya Bridge Server 已停止")
    
    def _server_loop(self):
        """服务器主循环"""
        while self.running:
            try:
                client_socket, address = self.server_socket.accept()
                print(f"📞 客户端连接: {address}")
                
                # 处理客户端请求
                self._handle_client(client_socket)
                
            except Exception as e:
                if self.running:
                    print(f"❌ 服务器错误: {e}")
                break
    
    def _handle_client(self, client_socket):
        """处理客户端请求"""
        try:
            # 接收请求
            length_data = client_socket.recv(4)
            if not length_data:
                return
            
            message_length = struct.unpack('!I', length_data)[0]
            message_data = client_socket.recv(message_length)
            
            if not message_data:
                return
            
            # 解析请求
            request = json.loads(message_data.decode('utf-8'))
            print(f"📨 收到请求: {request.get('command', 'unknown')}")
            
            # 处理请求
            response = self._process_request(request)
            
            # 发送响应
            response_json = json.dumps(response)
            response_bytes = response_json.encode('utf-8')
            response_length = struct.pack('!I', len(response_bytes))
            
            client_socket.sendall(response_length + response_bytes)
            print(f"📤 发送响应: {response.get('success', False)}")
            
        except Exception as e:
            print(f"❌ 处理客户端请求失败: {e}")
        finally:
            client_socket.close()
    
    def _process_request(self, request):
        """处理具体请求"""
        command = request.get('command')
        data = request.get('data', {})
        
        try:
            if command == 'get_bone_positions':
                return self._get_bone_positions(data)
            elif command == 'get_scene_data':
                return self._get_scene_data(data)
            else:
                return {'success': False, 'error': f'未知命令: {command}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _get_bone_positions(self, data):
        """获取骨骼位置"""
        try:
            bone_names = data.get('bone_names', [])
            
            if not bone_names:
                # 获取所有关节
                bone_names = cmds.ls(type='joint')
            
            positions = {}
            for bone in bone_names:
                if cmds.objExists(bone):
                    pos = cmds.xform(bone, query=True, worldSpace=True, translation=True)
                    positions[bone] = pos
            
            print(f"📍 获取了 {len(positions)} 个骨骼位置")
            return {
                'success': True,
                'bone_positions': positions
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _get_scene_data(self, data):
        """获取场景数据"""
        try:
            # 获取骨骼位置
            bone_result = self._get_bone_positions(data)
            
            scene_data = {
                'timestamp': time.time(),
                'bone_positions': bone_result.get('bone_positions', {}),
                'camera_info': {},
                'viewport_image': None,
                'depth_buffer': None
            }
            
            return {
                'success': True,
                'data': scene_data
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def test_maya_bridge():
    """测试Maya Bridge功能"""
    print("🎭 开始测试Maya Bridge...")
    
    # 初始化Maya
    if not initialize_maya():
        return False
    
    # 创建测试场景
    joints = create_test_scene()
    if not joints:
        return False
    
    # 启动Bridge服务器
    bridge = SimpleMayaBridge()
    if not bridge.start():
        return False
    
    # 等待一下让服务器完全启动
    time.sleep(2)
    
    # 测试连接
    print("🔗 测试连接...")
    try:
        # 创建测试客户端
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.connect(('localhost', 7001))
        
        # 发送测试请求
        request = {
            'command': 'get_bone_positions',
            'data': {}
        }
        
        request_json = json.dumps(request)
        request_bytes = request_json.encode('utf-8')
        request_length = struct.pack('!I', len(request_bytes))
        
        client_socket.sendall(request_length + request_bytes)
        
        # 接收响应
        length_data = client_socket.recv(4)
        response_length = struct.unpack('!I', length_data)[0]
        response_data = client_socket.recv(response_length)
        
        response = json.loads(response_data.decode('utf-8'))
        
        if response.get('success'):
            bone_positions = response.get('bone_positions', {})
            print(f"✅ 连接测试成功！获取到 {len(bone_positions)} 个骨骼位置")
            
            # 显示前几个骨骼位置
            for i, (bone, pos) in enumerate(list(bone_positions.items())[:5]):
                print(f"   {bone}: {pos}")
            
            client_socket.close()
            bridge.stop()
            return True
        else:
            print(f"❌ 服务器响应错误: {response.get('error')}")
            client_socket.close()
            bridge.stop()
            return False
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        bridge.stop()
        return False

if __name__ == "__main__":
    success = test_maya_bridge()
    if success:
        print("\n🎉 Maya Bridge 测试成功！")
        print("现在可以在ComfyUI中使用Maya Bridge节点了。")
    else:
        print("\n❌ Maya Bridge 测试失败！")
    
    # 清理Maya
    try:
        maya.standalone.uninitialize()
    except:
        pass
