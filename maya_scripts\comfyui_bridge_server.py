"""
ComfyUI Bridge Server for Maya
Run this script in Maya to enable communication with ComfyUI
"""

import maya.cmds as cmds
import maya.OpenMayaUI as omui
import maya.OpenMaya as om
import socket
import json
import struct
import threading
import time
import base64
import tempfile
import os
from typing import Dict, List, Any, Optional

class ComfyUIBridgeServer:
    """Server running in Maya to handle ComfyUI requests"""
    
    def __init__(self, port: int = 7001):
        self.port = port
        self.server_socket = None
        self.running = False
        self.server_thread = None
        
    def start_server(self):
        """Start the bridge server"""
        if self.running:
            print("Server is already running")
            return
            
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', self.port))
            self.server_socket.listen(5)
            
            self.running = True
            self.server_thread = threading.Thread(target=self._server_loop)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            print(f"ComfyUI Bridge Server started on port {self.port}")
            
        except Exception as e:
            print(f"Failed to start server: {e}")
            self.running = False
    
    def stop_server(self):
        """Stop the bridge server"""
        self.running = False
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
            self.server_socket = None
        print("ComfyUI Bridge Server stopped")
    
    def _server_loop(self):
        """Main server loop"""
        while self.running:
            try:
                client_socket, address = self.server_socket.accept()
                print(f"Client connected from {address}")
                
                # Handle client in separate thread
                client_thread = threading.Thread(
                    target=self._handle_client, 
                    args=(client_socket,)
                )
                client_thread.daemon = True
                client_thread.start()
                
            except Exception as e:
                if self.running:
                    print(f"Server error: {e}")
                break
    
    def _handle_client(self, client_socket):
        """Handle individual client connection"""
        try:
            while self.running:
                # Receive message length
                length_data = self._recv_exact(client_socket, 4)
                if not length_data:
                    break
                    
                message_length = struct.unpack('!I', length_data)[0]
                
                # Receive message
                message_data = self._recv_exact(client_socket, message_length)
                if not message_data:
                    break
                
                # Process message
                try:
                    message = json.loads(message_data.decode('utf-8'))
                    response = self._process_command(message)
                except Exception as e:
                    response = {"success": False, "error": str(e)}
                
                # Send response
                response_json = json.dumps(response)
                response_bytes = response_json.encode('utf-8')
                response_length = struct.pack('!I', len(response_bytes))
                
                client_socket.sendall(response_length + response_bytes)
                
        except Exception as e:
            print(f"Client handling error: {e}")
        finally:
            client_socket.close()
    
    def _recv_exact(self, socket, length: int) -> Optional[bytes]:
        """Receive exact number of bytes"""
        data = b''
        while len(data) < length:
            chunk = socket.recv(length - len(data))
            if not chunk:
                return None
            data += chunk
        return data
    
    def _process_command(self, message: Dict) -> Dict:
        """Process incoming command"""
        command = message.get("command")
        data = message.get("data", {})
        
        try:
            if command == "get_viewport_image":
                return self._get_viewport_image(data)
            elif command == "get_bone_positions":
                return self._get_bone_positions(data)
            elif command == "get_camera_info":
                return self._get_camera_info(data)
            elif command == "get_depth_buffer":
                return self._get_depth_buffer(data)
            elif command == "get_scene_data":
                return self._get_scene_data(data)
            else:
                return {"success": False, "error": f"Unknown command: {command}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _get_viewport_image(self, data: Dict) -> Dict:
        """Capture viewport image"""
        width = data.get("width", 512)
        height = data.get("height", 512)
        
        try:
            # Create temporary file
            temp_file = tempfile.mktemp(suffix='.jpg')
            
            # Capture viewport using playblast
            cmds.playblast(
                filename=temp_file,
                viewer=False,
                showOrnaments=False,
                frame=[cmds.currentTime(query=True)],
                widthHeight=[width, height],
                percent=100,
                compression='jpg',
                quality=90,
                clearCache=True
            )
            
            # Read and encode image
            with open(temp_file, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            # Clean up
            os.remove(temp_file)
            
            return {
                "success": True,
                "image_data": image_data,
                "width": width,
                "height": height
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _get_bone_positions(self, data: Dict) -> Dict:
        """Get bone world positions"""
        bone_names = data.get("bone_names", [])
        
        try:
            positions = {}
            
            # If no specific bones specified, get all joints
            if not bone_names:
                bone_names = cmds.ls(type='joint')
            
            for bone in bone_names:
                if cmds.objExists(bone):
                    # Get world space position
                    pos = cmds.xform(bone, query=True, worldSpace=True, translation=True)
                    positions[bone] = pos
            
            return {
                "success": True,
                "bone_positions": positions
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _get_camera_info(self, data: Dict) -> Dict:
        """Get current camera information"""
        try:
            # Get current camera
            panel = cmds.getPanel(withFocus=True)
            if cmds.getPanel(typeOf=panel) == 'modelPanel':
                camera = cmds.modelPanel(panel, query=True, camera=True)
            else:
                camera = 'persp'  # Default camera
            
            # Get camera transform and shape
            camera_transform = camera
            camera_shapes = cmds.listRelatives(camera_transform, shapes=True, type='camera')
            
            if not camera_shapes:
                return {"success": False, "error": "No camera found"}
            
            camera_shape = camera_shapes[0]
            
            # Get camera attributes
            info = {
                'name': camera_transform,
                'position': cmds.xform(camera_transform, query=True, worldSpace=True, translation=True),
                'rotation': cmds.xform(camera_transform, query=True, worldSpace=True, rotation=True),
                'focal_length': cmds.getAttr(camera_shape + '.focalLength'),
                'horizontal_film_aperture': cmds.getAttr(camera_shape + '.horizontalFilmAperture'),
                'vertical_film_aperture': cmds.getAttr(camera_shape + '.verticalFilmAperture'),
                'near_clip': cmds.getAttr(camera_shape + '.nearClipPlane'),
                'far_clip': cmds.getAttr(camera_shape + '.farClipPlane')
            }
            
            return {
                "success": True,
                "camera_info": info
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _get_depth_buffer(self, data: Dict) -> Dict:
        """Get depth buffer from Maya viewport"""
        width = data.get("width", 512)
        height = data.get("height", 512)

        try:
            # Import depth extractor
            try:
                from depth_buffer_extractor import extract_maya_depth_buffer
                depth_data = extract_maya_depth_buffer(width, height)

                if depth_data is not None:
                    return {
                        "success": True,
                        "depth_data": depth_data,
                        "width": width,
                        "height": height,
                        "method": "maya_scene_analysis"
                    }
            except ImportError:
                pass

            # Fallback: Simple depth estimation
            depth_data = self._simple_depth_estimation(width, height)

            return {
                "success": True,
                "depth_data": depth_data,
                "width": width,
                "height": height,
                "method": "simple_estimation"
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def _simple_depth_estimation(self, width: int, height: int) -> List[float]:
        """Simple depth estimation based on object distances"""
        try:
            # Get camera position
            panel = cmds.getPanel(withFocus=True)
            if cmds.getPanel(typeOf=panel) == 'modelPanel':
                camera = cmds.modelPanel(panel, query=True, camera=True)
            else:
                camera = 'persp'

            camera_pos = cmds.xform(camera, query=True, worldSpace=True, translation=True)

            # Get all visible objects
            all_objects = cmds.ls(dag=True, visible=True, type='transform')

            if not all_objects:
                return [1.0] * (width * height)

            # Calculate average distance to objects
            distances = []
            for obj in all_objects:
                try:
                    obj_pos = cmds.xform(obj, query=True, worldSpace=True, translation=True)
                    if obj_pos:
                        dist = ((obj_pos[0] - camera_pos[0])**2 +
                               (obj_pos[1] - camera_pos[1])**2 +
                               (obj_pos[2] - camera_pos[2])**2)**0.5
                        distances.append(dist)
                except:
                    continue

            if not distances:
                return [1.0] * (width * height)

            # Normalize distances
            min_dist = min(distances)
            max_dist = max(distances)
            avg_dist = sum(distances) / len(distances)

            # Create depth map with some variation
            depth_data = []
            center_x, center_y = width // 2, height // 2

            for y in range(height):
                for x in range(width):
                    # Distance from center
                    dist_from_center = ((x - center_x)**2 + (y - center_y)**2)**0.5
                    max_dist_from_center = ((center_x)**2 + (center_y)**2)**0.5

                    # Normalize and create depth value
                    if max_dist_from_center > 0:
                        normalized_dist = dist_from_center / max_dist_from_center
                        depth_value = 0.3 + 0.4 * normalized_dist  # Range from 0.3 to 0.7
                    else:
                        depth_value = 0.5

                    depth_data.append(depth_value)

            return depth_data

        except Exception as e:
            print(f"Error in simple depth estimation: {e}")
            return [1.0] * (width * height)
    
    def _get_scene_data(self, data: Dict) -> Dict:
        """Get comprehensive scene data"""
        try:
            bone_names = data.get("bone_names", [])
            width = data.get("image_width", 512)
            height = data.get("image_height", 512)
            include_viewport = data.get("include_viewport", True)
            include_depth = data.get("include_depth", True)
            
            result_data = {
                "timestamp": time.time()
            }
            
            # Get bone positions
            bone_result = self._get_bone_positions({"bone_names": bone_names})
            if bone_result.get("success"):
                result_data["bone_positions"] = bone_result["bone_positions"]
            
            # Get camera info
            camera_result = self._get_camera_info({})
            if camera_result.get("success"):
                result_data["camera_info"] = camera_result["camera_info"]
            
            # Get viewport image
            if include_viewport:
                viewport_result = self._get_viewport_image({"width": width, "height": height})
                if viewport_result.get("success"):
                    result_data["viewport_image_data"] = viewport_result["image_data"]
            
            # Get depth buffer
            if include_depth:
                depth_result = self._get_depth_buffer({"width": width, "height": height})
                if depth_result.get("success"):
                    result_data["depth_data"] = depth_result["depth_data"]
            
            return {
                "success": True,
                "data": result_data
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}


# Global server instance
_bridge_server = None

def start_comfyui_bridge(port: int = 7001):
    """Start ComfyUI bridge server"""
    global _bridge_server
    
    if _bridge_server and _bridge_server.running:
        print("Bridge server is already running")
        return
    
    _bridge_server = ComfyUIBridgeServer(port)
    _bridge_server.start_server()

def stop_comfyui_bridge():
    """Stop ComfyUI bridge server"""
    global _bridge_server
    
    if _bridge_server:
        _bridge_server.stop_server()
        _bridge_server = None

def restart_comfyui_bridge(port: int = 7001):
    """Restart ComfyUI bridge server"""
    stop_comfyui_bridge()
    time.sleep(1)
    start_comfyui_bridge(port)

def get_bridge_status():
    """Get bridge server status"""
    global _bridge_server
    
    if _bridge_server and _bridge_server.running:
        return f"Bridge server running on port {_bridge_server.port}"
    else:
        return "Bridge server not running"

# Auto-start server when script is loaded
if __name__ == "__main__":
    start_comfyui_bridge()
    print("ComfyUI Bridge Server initialized. Use start_comfyui_bridge() to start.")

# Maya shelf button commands
def create_bridge_shelf():
    """Create Maya shelf with bridge controls"""
    shelf_name = "ComfyUI_Bridge"
    
    # Delete existing shelf if it exists
    if cmds.shelfLayout(shelf_name, exists=True):
        cmds.deleteUI(shelf_name)
    
    # Create new shelf
    shelf = cmds.shelfLayout(shelf_name, parent="ShelfLayout")
    
    # Start server button
    cmds.shelfButton(
        parent=shelf,
        label="Start Bridge",
        command="start_comfyui_bridge()",
        annotation="Start ComfyUI Bridge Server",
        image="play.png"
    )
    
    # Stop server button
    cmds.shelfButton(
        parent=shelf,
        label="Stop Bridge",
        command="stop_comfyui_bridge()",
        annotation="Stop ComfyUI Bridge Server",
        image="stop.png"
    )
    
    # Status button
    cmds.shelfButton(
        parent=shelf,
        label="Status",
        command="print(get_bridge_status())",
        annotation="Check Bridge Server Status",
        image="info.png"
    )
    
    print(f"Created {shelf_name} shelf with bridge controls")

# Create shelf on script load
try:
    create_bridge_shelf()
except:
    pass  # Ignore errors if running outside Maya
