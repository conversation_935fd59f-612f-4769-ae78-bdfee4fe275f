#!/usr/bin/env python3
"""
Maya Bridge Plugin Test Suite
Tests the functionality of the Maya Bridge plugin
"""

import unittest
import json
import numpy as np
import tempfile
import os
import sys
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add src directory to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

class TestBoneMappingPresets(unittest.TestCase):
    """Test bone mapping presets functionality"""
    
    def setUp(self):
        from bone_mapping_presets import BoneMappingManager
        self.manager = BoneMappingManager()
    
    def test_get_preset_names(self):
        """Test getting preset names"""
        names = self.manager.get_preset_names()
        self.assertIn("standard", names)
        self.assertIn("mixamo", names)
        self.assertIn("humanik", names)
    
    def test_get_preset(self):
        """Test getting a specific preset"""
        preset = self.manager.get_preset("standard")
        self.assertIsNotNone(preset)
        self.assertIn("mapping", preset)
        self.assertIn("name", preset)
        self.assertIn("description", preset)
    
    def test_validate_mapping(self):
        """Test mapping validation"""
        # Valid mapping
        valid_mapping = {"head": 0, "neck": 1}
        is_valid, errors = self.manager.validate_mapping(valid_mapping)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        
        # Invalid mapping - out of range index
        invalid_mapping = {"head": 20}
        is_valid, errors = self.manager.validate_mapping(invalid_mapping)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
        
        # Invalid mapping - duplicate indices
        duplicate_mapping = {"head": 0, "neck": 0}
        is_valid, errors = self.manager.validate_mapping(duplicate_mapping)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
    
    def test_create_mapping_from_json(self):
        """Test creating mapping from JSON string"""
        json_str = '{"head": 0, "neck": 1}'
        mapping, errors = self.manager.create_mapping_from_json(json_str)
        self.assertIsNotNone(mapping)
        self.assertEqual(len(errors), 0)
        self.assertEqual(mapping["head"], 0)
        
        # Invalid JSON
        invalid_json = '{"head": 0, "neck":}'
        mapping, errors = self.manager.create_mapping_from_json(invalid_json)
        self.assertIsNone(mapping)
        self.assertGreater(len(errors), 0)
    
    def test_suggest_bone_names(self):
        """Test bone name suggestions"""
        available_bones = ["Character_Head", "Character_Neck", "Character_RightShoulder"]
        suggestions = self.manager.suggest_bone_names(available_bones)
        
        # Should find some matches
        self.assertGreater(len(suggestions), 0)


class TestMayaBridgeConfig(unittest.TestCase):
    """Test Maya Bridge configuration"""
    
    def setUp(self):
        from maya_bridge_config import MayaBridgeConfig
        # Use temporary file for testing
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        self.temp_file.close()
        self.config = MayaBridgeConfig(self.temp_file.name)
    
    def tearDown(self):
        os.unlink(self.temp_file.name)
    
    def test_default_config(self):
        """Test default configuration values"""
        self.assertEqual(self.config.get("maya_connection.default_host"), "localhost")
        self.assertEqual(self.config.get("maya_connection.default_port"), 7001)
        self.assertEqual(self.config.get("bone_mapping.default_preset"), "standard")
    
    def test_get_set_config(self):
        """Test getting and setting configuration values"""
        # Set a value
        self.assertTrue(self.config.set("test.value", 42))
        
        # Get the value
        self.assertEqual(self.config.get("test.value"), 42)
        
        # Get non-existent value with default
        self.assertEqual(self.config.get("non.existent", "default"), "default")
    
    def test_save_load_config(self):
        """Test saving and loading configuration"""
        # Set a test value
        self.config.set("test.save_load", "test_value")
        
        # Save configuration
        self.assertTrue(self.config.save_config())
        
        # Create new config instance and load
        from maya_bridge_config import MayaBridgeConfig
        new_config = MayaBridgeConfig(self.temp_file.name)
        
        # Check if value was loaded
        self.assertEqual(new_config.get("test.save_load"), "test_value")
    
    def test_validate_config(self):
        """Test configuration validation"""
        is_valid, errors = self.config.validate_config()
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        
        # Set invalid port
        self.config.set("maya_connection.default_port", 99999)
        is_valid, errors = self.config.validate_config()
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)


class TestMayaConnector(unittest.TestCase):
    """Test Maya connector functionality"""
    
    def setUp(self):
        from maya_connector import MayaConnector
        self.connector = MayaConnector("localhost", 7001)
    
    @patch('socket.socket')
    def test_connect_success(self, mock_socket):
        """Test successful connection to Maya"""
        mock_socket_instance = Mock()
        mock_socket.return_value = mock_socket_instance
        
        result = self.connector.connect()
        self.assertTrue(result)
        self.assertTrue(self.connector.connected)
        
        mock_socket_instance.connect.assert_called_once_with(("localhost", 7001))
    
    @patch('socket.socket')
    def test_connect_failure(self, mock_socket):
        """Test failed connection to Maya"""
        mock_socket_instance = Mock()
        mock_socket.return_value = mock_socket_instance
        mock_socket_instance.connect.side_effect = ConnectionRefusedError()
        
        result = self.connector.connect()
        self.assertFalse(result)
        self.assertFalse(self.connector.connected)
    
    def test_send_command_not_connected(self):
        """Test sending command when not connected"""
        self.connector.connected = False
        
        with patch.object(self.connector, 'connect', return_value=False):
            result = self.connector.send_command("test_command")
            self.assertIsNone(result)


class TestMayaBridgeNode(unittest.TestCase):
    """Test Maya Bridge ComfyUI node"""
    
    def setUp(self):
        # Mock the ComfyUI imports
        sys.modules['comfy'] = Mock()
        sys.modules['comfy.model_management'] = Mock()
        
        # Import after mocking
        from node_wrappers.maya_bridge import MayaBridgePreprocessor
        self.node = MayaBridgePreprocessor()
    
    def test_input_types(self):
        """Test node input types definition"""
        from node_wrappers.maya_bridge import MayaBridgePreprocessor
        input_types = MayaBridgePreprocessor.INPUT_TYPES()
        
        self.assertIn("required", input_types)
        required = input_types["required"]
        
        # Check for essential inputs
        self.assertIn("maya_host", required)
        self.assertIn("maya_port", required)
        self.assertIn("resolution", required)
    
    def test_get_mock_bone_positions(self):
        """Test mock bone positions generation"""
        mock_positions = self.node._get_mock_bone_positions()
        
        self.assertIsInstance(mock_positions, dict)
        self.assertIn("head", mock_positions)
        self.assertIn("neck", mock_positions)
        
        # Check position format (x, y, z)
        head_pos = mock_positions["head"]
        self.assertEqual(len(head_pos), 3)
        self.assertIsInstance(head_pos[0], (int, float))
    
    def test_world_to_screen_conversion(self):
        """Test 3D to 2D coordinate conversion"""
        world_pos = (0.0, 5.0, 0.0)
        screen_pos = self.node._world_to_screen(world_pos)
        
        self.assertEqual(len(screen_pos), 2)
        self.assertGreaterEqual(screen_pos[0], 0.0)
        self.assertLessEqual(screen_pos[0], 1.0)
        self.assertGreaterEqual(screen_pos[1], 0.0)
        self.assertLessEqual(screen_pos[1], 1.0)
    
    def test_detect_character_facing(self):
        """Test character facing detection"""
        # Create mock keypoints for front-facing character
        front_keypoints = [None] * 18
        front_keypoints[2] = (0.3, 0.5)  # Right shoulder
        front_keypoints[5] = (0.7, 0.5)  # Left shoulder
        
        facing = self.node._detect_character_facing(front_keypoints)
        self.assertEqual(facing, "front")
        
        # Create mock keypoints for back-facing character
        back_keypoints = [None] * 18
        back_keypoints[2] = (0.7, 0.5)  # Right shoulder (appears on left)
        back_keypoints[5] = (0.3, 0.5)  # Left shoulder (appears on right)
        
        facing = self.node._detect_character_facing(back_keypoints)
        self.assertEqual(facing, "back")
    
    def test_flip_keypoints_horizontal(self):
        """Test horizontal keypoint flipping"""
        original_keypoints = [None] * 18
        original_keypoints[0] = (0.3, 0.5)  # Nose
        original_keypoints[2] = (0.2, 0.6)  # Right shoulder
        original_keypoints[5] = (0.8, 0.6)  # Left shoulder
        
        flipped_keypoints = self.node._flip_keypoints_horizontal(original_keypoints)
        
        # Check if x-coordinates are flipped
        self.assertAlmostEqual(flipped_keypoints[0][0], 0.7, places=1)  # Nose
        
        # Check if left/right keypoints are swapped
        self.assertEqual(flipped_keypoints[2], (0.2, 0.6))  # Was left shoulder
        self.assertEqual(flipped_keypoints[5], (0.8, 0.6))  # Was right shoulder
    
    def test_create_openpose_json(self):
        """Test OpenPose JSON format creation"""
        keypoints = [(0.5, 0.3), (0.5, 0.4)] + [None] * 16
        
        pose_json = self.node._create_openpose_json(keypoints, 512, 512)
        
        self.assertIn("people", pose_json)
        self.assertIn("canvas_height", pose_json)
        self.assertIn("canvas_width", pose_json)
        
        self.assertEqual(pose_json["canvas_height"], 512)
        self.assertEqual(pose_json["canvas_width"], 512)
        
        people = pose_json["people"]
        self.assertEqual(len(people), 1)
        
        person = people[0]
        self.assertIn("pose_keypoints_2d", person)
        
        # Check keypoint format [x, y, confidence, x, y, confidence, ...]
        pose_keypoints = person["pose_keypoints_2d"]
        self.assertEqual(len(pose_keypoints), 18 * 3)  # 18 keypoints * 3 values each
        
        # First keypoint should be (256, 153.6, 1.0)
        self.assertAlmostEqual(pose_keypoints[0], 256.0, places=0)  # x
        self.assertAlmostEqual(pose_keypoints[1], 153.6, places=0)  # y
        self.assertEqual(pose_keypoints[2], 1.0)  # confidence


class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def test_full_workflow_mock(self):
        """Test full workflow with mocked Maya connection"""
        # Mock ComfyUI modules
        sys.modules['comfy'] = Mock()
        sys.modules['comfy.model_management'] = Mock()
        
        from node_wrappers.maya_bridge import MayaBridgePreprocessor
        node = MayaBridgePreprocessor()
        
        # Mock Maya data
        mock_maya_data = {
            "viewport_image": None,
            "bone_positions": {
                "head": (0.0, 8.0, 0.0),
                "neck": (0.0, 7.0, 0.0),
                "rightShoulder": (2.0, 6.5, 0.0),
                "leftShoulder": (-2.0, 6.5, 0.0)
            },
            "camera_info": {},
            "depth_buffer": None
        }
        
        # Mock the Maya connection
        with patch.object(node, '_get_maya_data', return_value=mock_maya_data):
            # Create a mock image
            mock_image = np.zeros((512, 512, 3), dtype=np.uint8)
            
            # Process the data
            result = node.process_maya_data(
                mock_image,
                maya_host="localhost",
                maya_port=7001,
                bone_mapping_preset="standard",
                resolution=512
            )
            
            # Check results
            self.assertEqual(len(result), 4)  # pose_image, depth_image, pose_keypoints, debug_info
            
            pose_image, depth_image, pose_keypoints, debug_info = result
            
            # Verify outputs
            self.assertIsNotNone(pose_image)
            self.assertIsNotNone(depth_image)
            self.assertIsNotNone(pose_keypoints)
            self.assertIsInstance(debug_info, str)
            
            # Check pose keypoints structure
            self.assertIn("people", pose_keypoints)
            self.assertIn("canvas_height", pose_keypoints)
            self.assertIn("canvas_width", pose_keypoints)


def run_tests():
    """Run all tests"""
    print("🧪 Running Maya Bridge Plugin Tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_cases = [
        TestBoneMappingPresets,
        TestMayaBridgeConfig,
        TestMayaConnector,
        TestMayaBridgeNode,
        TestIntegration
    ]
    
    for test_case in test_cases:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_case)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
    
    if result.errors:
        print(f"\n💥 ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('\\n')[-2]}")
    
    if not result.failures and not result.errors:
        print(f"\n✅ All tests passed!")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
