#!/usr/bin/env python
"""
Maya Bridge Server Test - Full functionality test
"""

import sys
import os
import socket
import json
import struct
import threading
import time

def create_maya_scene():
    """Create a test Maya scene with character skeleton"""
    try:
        import maya.standalone
        import maya.cmds as cmds
        
        maya.standalone.initialize()
        cmds.file(new=True, force=True)
        
        # Create character skeleton
        joints = {}
        
        # Root and spine
        joints['root'] = cmds.joint(name='root', position=[0, 0, 0])
        cmds.select(joints['root'])
        joints['spine'] = cmds.joint(name='spine', position=[0, 3, 0])
        joints['neck'] = cmds.joint(name='neck', position=[0, 7, 0])
        joints['head'] = cmds.joint(name='head', position=[0, 8, 0])
        
        # Left arm
        cmds.select(joints['spine'])
        joints['leftShoulder'] = cmds.joint(name='leftShoulder', position=[-2, 6.5, 0])
        joints['leftElbow'] = cmds.joint(name='leftElbow', position=[-3.5, 5, 0])
        joints['leftWrist'] = cmds.joint(name='leftWrist', position=[-4.5, 3.5, 0])
        
        # Right arm
        cmds.select(joints['spine'])
        joints['rightShoulder'] = cmds.joint(name='rightShoulder', position=[2, 6.5, 0])
        joints['rightElbow'] = cmds.joint(name='rightElbow', position=[3.5, 5, 0])
        joints['rightWrist'] = cmds.joint(name='rightWrist', position=[4.5, 3.5, 0])
        
        # Left leg
        cmds.select(joints['root'])
        joints['leftHip'] = cmds.joint(name='leftHip', position=[-1, 0, 0])
        joints['leftKnee'] = cmds.joint(name='leftKnee', position=[-1, -3, 0])
        joints['leftAnkle'] = cmds.joint(name='leftAnkle', position=[-1, -6, 0])
        
        # Right leg
        cmds.select(joints['root'])
        joints['rightHip'] = cmds.joint(name='rightHip', position=[1, 0, 0])
        joints['rightKnee'] = cmds.joint(name='rightKnee', position=[1, -3, 0])
        joints['rightAnkle'] = cmds.joint(name='rightAnkle', position=[1, -6, 0])
        
        print("SUCCESS: Created character skeleton with " + str(len(joints)) + " joints")
        return True
        
    except Exception as e:
        print("ERROR: Failed to create Maya scene: " + str(e))
        return False

class MayaBridgeServer:
    """Maya Bridge Server for ComfyUI communication"""
    
    def __init__(self, port=7001):
        self.port = port
        self.running = False
        self.server_socket = None
        self.server_thread = None
    
    def start(self):
        """Start the bridge server"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', self.port))
            self.server_socket.listen(5)
            
            self.running = True
            self.server_thread = threading.Thread(target=self._server_loop)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            print("SUCCESS: Maya Bridge Server started on port " + str(self.port))
            return True
            
        except Exception as e:
            print("ERROR: Failed to start server: " + str(e))
            return False
    
    def stop(self):
        """Stop the bridge server"""
        self.running = False
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        print("INFO: Maya Bridge Server stopped")
    
    def _server_loop(self):
        """Main server loop"""
        while self.running:
            try:
                client_socket, address = self.server_socket.accept()
                print("INFO: Client connected from " + str(address))
                
                # Handle client in separate thread
                client_thread = threading.Thread(
                    target=self._handle_client, 
                    args=(client_socket,)
                )
                client_thread.daemon = True
                client_thread.start()
                
            except Exception as e:
                if self.running:
                    print("ERROR: Server error: " + str(e))
                break
    
    def _handle_client(self, client_socket):
        """Handle individual client connection"""
        try:
            while self.running:
                # Receive message length
                length_data = self._recv_exact(client_socket, 4)
                if not length_data:
                    break
                    
                message_length = struct.unpack('!I', length_data)[0]
                
                # Receive message
                message_data = self._recv_exact(client_socket, message_length)
                if not message_data:
                    break
                
                # Process message
                try:
                    message = json.loads(message_data.decode('utf-8'))
                    response = self._process_command(message)
                except Exception as e:
                    response = {"success": False, "error": str(e)}
                
                # Send response
                response_json = json.dumps(response)
                response_bytes = response_json.encode('utf-8')
                response_length = struct.pack('!I', len(response_bytes))
                
                client_socket.sendall(response_length + response_bytes)
                
        except Exception as e:
            print("ERROR: Client handling error: " + str(e))
        finally:
            client_socket.close()
    
    def _recv_exact(self, socket, length):
        """Receive exact number of bytes"""
        data = b''
        while len(data) < length:
            chunk = socket.recv(length - len(data))
            if not chunk:
                return None
            data += chunk
        return data
    
    def _process_command(self, message):
        """Process incoming command"""
        command = message.get("command")
        data = message.get("data", {})
        
        try:
            if command == "get_bone_positions":
                return self._get_bone_positions(data)
            elif command == "get_scene_data":
                return self._get_scene_data(data)
            else:
                return {"success": False, "error": "Unknown command: " + str(command)}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _get_bone_positions(self, data):
        """Get bone world positions"""
        try:
            import maya.cmds as cmds
            
            bone_names = data.get("bone_names", [])
            
            if not bone_names:
                bone_names = cmds.ls(type='joint')
            
            positions = {}
            for bone in bone_names:
                if cmds.objExists(bone):
                    pos = cmds.xform(bone, query=True, worldSpace=True, translation=True)
                    positions[bone] = pos
            
            print("INFO: Retrieved " + str(len(positions)) + " bone positions")
            return {
                "success": True,
                "bone_positions": positions
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _get_scene_data(self, data):
        """Get comprehensive scene data"""
        try:
            bone_names = data.get("bone_names", [])
            
            # Get bone positions
            bone_result = self._get_bone_positions({"bone_names": bone_names})
            
            result_data = {
                "timestamp": time.time(),
                "bone_positions": bone_result.get("bone_positions", {}),
                "camera_info": {},
                "viewport_image": None,
                "depth_buffer": None
            }
            
            return {
                "success": True,
                "data": result_data
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

def test_comfyui_connection():
    """Test connection with ComfyUI-style client"""
    try:
        print("Testing ComfyUI-style connection...")
        
        # Create client socket
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.connect(('localhost', 7001))
        
        # Send get_scene_data request
        request = {
            "command": "get_scene_data",
            "data": {
                "bone_names": [],
                "image_width": 512,
                "image_height": 512,
                "include_viewport": True,
                "include_depth": True
            }
        }
        
        request_json = json.dumps(request)
        request_bytes = request_json.encode('utf-8')
        request_length = struct.pack('!I', len(request_bytes))
        
        client_socket.sendall(request_length + request_bytes)
        
        # Receive response
        length_data = client_socket.recv(4)
        response_length = struct.unpack('!I', length_data)[0]
        response_data = client_socket.recv(response_length)
        
        response = json.loads(response_data.decode('utf-8'))
        
        if response.get('success'):
            scene_data = response.get('data', {})
            bone_positions = scene_data.get('bone_positions', {})
            print("SUCCESS: ComfyUI connection test passed!")
            print("Retrieved " + str(len(bone_positions)) + " bone positions:")
            
            # Show first few bones
            count = 0
            for bone, pos in bone_positions.items():
                if count < 5:
                    print("  " + bone + ": " + str(pos))
                    count += 1
            
            client_socket.close()
            return True
        else:
            print("ERROR: Server response error: " + str(response.get('error')))
            client_socket.close()
            return False
            
    except Exception as e:
        print("ERROR: ComfyUI connection test failed: " + str(e))
        return False

def main():
    """Main test function"""
    print("Maya Bridge Server Full Test")
    print("=" * 50)
    
    # Create Maya scene
    print("\nStep 1: Creating Maya test scene...")
    if not create_maya_scene():
        return False
    
    # Start bridge server
    print("\nStep 2: Starting Maya Bridge Server...")
    server = MayaBridgeServer()
    if not server.start():
        return False
    
    # Wait for server to fully start
    time.sleep(2)
    
    # Test ComfyUI connection
    print("\nStep 3: Testing ComfyUI-style connection...")
    connection_ok = test_comfyui_connection()
    
    # Stop server
    print("\nStep 4: Stopping server...")
    server.stop()
    
    # Cleanup Maya
    try:
        import maya.standalone
        maya.standalone.uninitialize()
        print("SUCCESS: Maya cleanup complete")
    except:
        pass
    
    # Final result
    print("\n" + "=" * 50)
    if connection_ok:
        print("SUCCESS: Maya Bridge Server test completed successfully!")
        print("The plugin is ready for use with ComfyUI.")
        return True
    else:
        print("ERROR: Maya Bridge Server test failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
