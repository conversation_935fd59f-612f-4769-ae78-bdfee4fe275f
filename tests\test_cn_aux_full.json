{"last_node_id": 45, "last_link_id": 44, "nodes": [{"id": 24, "type": "PreviewImage", "pos": [843, -430], "size": [210, 246], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 23}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 25, "type": "PreviewImage", "pos": [1127, -346], "size": {"0": 210, "1": 26}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 24}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 26, "type": "PreviewImage", "pos": [832, -222], "size": {"0": 210, "1": 26}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 25}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 27, "type": "PreviewImage", "pos": [1144, -123], "size": {"0": 210, "1": 26}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 26}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 28, "type": "PreviewImage", "pos": [825, 56], "size": {"0": 210, "1": 26}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 27}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 29, "type": "PreviewImage", "pos": [1240, 246], "size": {"0": 210, "1": 26}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 28}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 30, "type": "PreviewImage", "pos": [855, 381], "size": {"0": 210, "1": 26}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 29}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 31, "type": "PreviewImage", "pos": [1248, 471], "size": {"0": 210, "1": 26}, "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 30}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 32, "type": "PreviewImage", "pos": [823, 632], "size": {"0": 210, "1": 26}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 31}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 33, "type": "PreviewImage", "pos": [1240, 737], "size": {"0": 210, "1": 26}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 32}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 34, "type": "PreviewImage", "pos": [844, 833], "size": {"0": 210, "1": 26}, "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 33}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 35, "type": "PreviewImage", "pos": [1216, 1023], "size": {"0": 210, "1": 26}, "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 34}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 36, "type": "PreviewImage", "pos": [838, 1175], "size": {"0": 210, "1": 26}, "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 35}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 37, "type": "PreviewImage", "pos": [1282, 1355], "size": {"0": 210, "1": 26}, "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 36}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 38, "type": "PreviewImage", "pos": [897, 1532], "size": {"0": 210, "1": 26}, "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 37}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 39, "type": "PreviewImage", "pos": [1336, 1704], "size": {"0": 210, "1": 26}, "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 38}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 40, "type": "PreviewImage", "pos": [859, 1840], "size": {"0": 210, "1": 26}, "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 39}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 41, "type": "PreviewImage", "pos": [1329, 1939], "size": {"0": 210, "1": 26}, "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 40}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 42, "type": "PreviewImage", "pos": [888, 2056], "size": {"0": 210, "1": 26}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 42}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 43, "type": "PreviewImage", "pos": [1278, 2191], "size": {"0": 210, "1": 26}, "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 41}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 2, "type": "PiDiNetPreprocessor", "pos": [420, -446], "size": {"0": 315, "1": 58}, "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [23], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "PiDiNetPreprocessor"}, "widgets_values": ["enable"]}, {"id": 3, "type": "ColorPreprocessor", "pos": [426, -332], "size": {"0": 210, "1": 26}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [24], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ColorPreprocessor"}}, {"id": 4, "type": "CannyEdgePreprocessor", "pos": [433, -245], "size": {"0": 315, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 3}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [25], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CannyEdgePreprocessor"}, "widgets_values": [100, 200]}, {"id": 5, "type": "SAMPreprocessor", "pos": [427, -108], "size": {"0": 210, "1": 26}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 4}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [26], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SAMPreprocessor"}}, {"id": 7, "type": "DWPreprocessor", "pos": [440, 95], "size": {"0": 315, "1": 106}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 6}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [27], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "DWPreprocessor"}, "widgets_values": ["enable", "enable", "enable"]}, {"id": 8, "type": "BinaryPreprocessor", "pos": [432, 266], "size": {"0": 315, "1": 58}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [28], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BinaryPreprocessor"}, "widgets_values": [100]}, {"id": 9, "type": "ScribblePreprocessor", "pos": [462, 376], "size": {"0": 210, "1": 26}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [29], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ScribblePreprocessor"}}, {"id": 10, "type": "M-LSDPreprocessor", "pos": [453, 497], "size": {"0": 315, "1": 82}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 9}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [30], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "M-LSDPreprocessor"}, "widgets_values": [0.1, 0.1]}, {"id": 11, "type": "UniFormer-SemSegPreprocessor", "pos": [479, 651], "size": {"0": 210, "1": 26}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 10}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [31], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "UniFormer-SemSegPreprocessor"}}, {"id": 12, "type": "Zoe-DepthMapPreprocessor", "pos": [483, 740], "size": {"0": 210, "1": 26}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 11}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [32], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Zoe-DepthMapPreprocessor"}}, {"id": 13, "type": "MiDaS-NormalMapPreprocessor", "pos": [463, 821], "size": {"0": 315, "1": 82}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 12}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [33], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MiDaS-NormalMapPreprocessor"}, "widgets_values": [6.283185307179586, 0.1]}, {"id": 14, "type": "MiDaS-DepthMapPreprocessor", "pos": [451, 1009], "size": {"0": 315, "1": 82}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 13}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [34], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MiDaS-DepthMapPreprocessor"}, "widgets_values": [6.283185307179586, 0.1]}, {"id": 15, "type": "OpenposePreprocessor", "pos": [466, 1177], "size": {"0": 315, "1": 106}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 14}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [35], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "OpenposePreprocessor"}, "widgets_values": ["enable", "enable", "enable"]}, {"id": 17, "type": "LeReS-DepthMapPreprocessor", "pos": [484, 1533], "size": {"0": 315, "1": 106}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 16}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [37], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "LeReS-DepthMapPreprocessor"}, "widgets_values": [0, 0, "enable"]}, {"id": 18, "type": "BAE-NormalMapPreprocessor", "pos": [510, 1729], "size": {"0": 210, "1": 26}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 17}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [38], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BAE-NormalMapPreprocessor"}}, {"id": 19, "type": "OneFormer-COCO-SemSegPreprocessor", "pos": [488, 1843], "size": {"0": 210, "1": 26}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 18}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [39], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "OneFormer-COCO-SemSegPreprocessor"}}, {"id": 20, "type": "OneFormer-ADE20K-SemSegPreprocessor", "pos": [470, 1941], "size": {"0": 210, "1": 26}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 19}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [40], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "OneFormer-ADE20K-SemSegPreprocessor"}}, {"id": 22, "type": "FakeScribblePreprocessor", "pos": [426, 2193], "size": {"0": 315, "1": 58}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 21}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [41], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "FakeScribblePreprocessor"}, "widgets_values": ["enable"]}, {"id": 21, "type": "HEDPreprocessor", "pos": [460, 2053], "size": {"0": 315, "1": 58}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 20}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [42], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "HEDPreprocessor"}, "widgets_values": ["enable"]}, {"id": 16, "type": "LineArtPreprocessor", "pos": [450, 1363], "size": {"0": 315, "1": 58}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 15}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [36], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "LineArtPreprocessor"}, "widgets_values": ["enable"]}, {"id": 45, "type": "PreviewImage", "pos": [886, 2316], "size": {"0": 210, "1": 26}, "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 43}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 44, "type": "TilePreprocessor", "pos": [419, 2320], "size": {"0": 315, "1": 58}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 44}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [43], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "TilePreprocessor"}, "widgets_values": [3]}, {"id": 1, "type": "LoadImage", "pos": [19, 298], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 44], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pose.png", "image"]}], "links": [[1, 1, 0, 2, 0, "IMAGE"], [2, 1, 0, 3, 0, "IMAGE"], [3, 1, 0, 4, 0, "IMAGE"], [4, 1, 0, 5, 0, "IMAGE"], [6, 1, 0, 7, 0, "IMAGE"], [7, 1, 0, 8, 0, "IMAGE"], [8, 1, 0, 9, 0, "IMAGE"], [9, 1, 0, 10, 0, "IMAGE"], [10, 1, 0, 11, 0, "IMAGE"], [11, 1, 0, 12, 0, "IMAGE"], [12, 1, 0, 13, 0, "IMAGE"], [13, 1, 0, 14, 0, "IMAGE"], [14, 1, 0, 15, 0, "IMAGE"], [15, 1, 0, 16, 0, "IMAGE"], [16, 1, 0, 17, 0, "IMAGE"], [17, 1, 0, 18, 0, "IMAGE"], [18, 1, 0, 19, 0, "IMAGE"], [19, 1, 0, 20, 0, "IMAGE"], [20, 1, 0, 21, 0, "IMAGE"], [21, 1, 0, 22, 0, "IMAGE"], [23, 2, 0, 24, 0, "IMAGE"], [24, 3, 0, 25, 0, "IMAGE"], [25, 4, 0, 26, 0, "IMAGE"], [26, 5, 0, 27, 0, "IMAGE"], [27, 7, 0, 28, 0, "IMAGE"], [28, 8, 0, 29, 0, "IMAGE"], [29, 9, 0, 30, 0, "IMAGE"], [30, 10, 0, 31, 0, "IMAGE"], [31, 11, 0, 32, 0, "IMAGE"], [32, 12, 0, 33, 0, "IMAGE"], [33, 13, 0, 34, 0, "IMAGE"], [34, 14, 0, 35, 0, "IMAGE"], [35, 15, 0, 36, 0, "IMAGE"], [36, 16, 0, 37, 0, "IMAGE"], [37, 17, 0, 38, 0, "IMAGE"], [38, 18, 0, 39, 0, "IMAGE"], [39, 19, 0, 40, 0, "IMAGE"], [40, 20, 0, 41, 0, "IMAGE"], [41, 22, 0, 43, 0, "IMAGE"], [42, 21, 0, 42, 0, "IMAGE"], [43, 44, 0, 45, 0, "IMAGE"], [44, 1, 0, 44, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}