#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的Maya Bridge测试
"""

import sys
import os

def test_maya_import():
    """测试Maya模块导入"""
    try:
        import maya.standalone
        print("✅ maya.standalone 导入成功")
        
        maya.standalone.initialize()
        print("✅ Maya standalone 初始化成功")
        
        import maya.cmds as cmds
        print("✅ maya.cmds 导入成功")
        
        # 创建新场景
        cmds.file(new=True, force=True)
        print("✅ 新场景创建成功")
        
        # 创建测试关节
        joint1 = cmds.joint(name='test_joint', position=[0, 0, 0])
        print(f"✅ 创建关节: {joint1}")
        
        # 获取关节位置
        pos = cmds.xform(joint1, query=True, worldSpace=True, translation=True)
        print(f"✅ 关节位置: {pos}")
        
        # 获取所有关节
        joints = cmds.ls(type='joint')
        print(f"✅ 场景中的关节: {joints}")
        
        maya.standalone.uninitialize()
        print("✅ Maya standalone 清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Maya测试失败: {e}")
        return False

def test_socket_server():
    """测试Socket服务器"""
    try:
        import socket
        import threading
        import time
        
        print("🔌 测试Socket服务器...")
        
        # 创建服务器
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_socket.bind(('localhost', 7001))
        server_socket.listen(1)
        
        print("✅ Socket服务器创建成功，端口: 7001")
        
        # 测试连接
        def test_client():
            time.sleep(1)  # 等待服务器启动
            try:
                client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                client.connect(('localhost', 7001))
                client.send(b'test')
                client.close()
                print("✅ 客户端连接测试成功")
            except Exception as e:
                print(f"❌ 客户端连接失败: {e}")
        
        # 启动客户端测试线程
        client_thread = threading.Thread(target=test_client)
        client_thread.start()
        
        # 接受连接
        client_socket, address = server_socket.accept()
        data = client_socket.recv(1024)
        print(f"✅ 服务器接收到数据: {data}")
        
        client_socket.close()
        server_socket.close()
        client_thread.join()
        
        print("✅ Socket测试完成")
        return True
        
    except Exception as e:
        print(f"❌ Socket测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎭 开始Maya Bridge简单测试...")
    print("=" * 50)
    
    # 测试Maya
    print("\n📋 测试1: Maya模块导入和基本功能")
    maya_ok = test_maya_import()
    
    # 测试Socket
    print("\n📋 测试2: Socket服务器功能")
    socket_ok = test_socket_server()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"Maya功能: {'✅ 通过' if maya_ok else '❌ 失败'}")
    print(f"Socket功能: {'✅ 通过' if socket_ok else '❌ 失败'}")
    
    if maya_ok and socket_ok:
        print("\n🎉 所有测试通过！Maya Bridge插件可以正常工作。")
        return True
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
