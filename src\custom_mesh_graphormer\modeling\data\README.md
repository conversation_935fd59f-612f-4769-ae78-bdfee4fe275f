
# Extra data
Adapted from open source project [GraphCMR](https://github.com/nkolot/GraphCMR/) and [Pose2Mesh](https://github.com/hongsukchoi/Pose2Mesh_RELEASE)

Our code requires additional data to run smoothly. 

### J_regressor_extra.npy
Joints regressor for joints or landmarks that are not included in the standard set of SMPL joints.

### J_regressor_h36m_correct.npy
Joints regressor reflecting the Human3.6M joints. 

### mesh_downsampling.npz
Extra file with precomputed downsampling for the SMPL body mesh.

### mano_downsampling.npz
Extra file with precomputed downsampling for the MANO hand mesh.

### basicModel_neutral_lbs_10_207_0_v1.0.0.pkl
SMPL neutral model. Please visit the official website to download the file [http://smplify.is.tue.mpg.de/](http://smplify.is.tue.mpg.de/)

### basicModel_m_lbs_10_207_0_v1.0.0.pkl
SMPL male model. Please visit the official website to download the file [https://smpl.is.tue.mpg.de/](https://smpl.is.tue.mpg.de/)

### basicModel_f_lbs_10_207_0_v1.0.0.pkl
SMPL female model. Please visit the official website to download the file [https://smpl.is.tue.mpg.de/](https://smpl.is.tue.mpg.de/)

### MANO_RIGHT.pkl
MANO hand model. Please visit the official website to download the file [https://mano.is.tue.mpg.de/](https://mano.is.tue.mpg.de/)

