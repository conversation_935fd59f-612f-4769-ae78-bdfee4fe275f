"""
Maya Bridge Plugin Initialization for ComfyUI
This file should be imported or integrated into the main __init__.py
"""

import os
import sys
from pathlib import Path

def initialize_maya_bridge():
    """Initialize Maya Bridge plugin components"""
    
    # Add src directory to Python path
    current_dir = Path(__file__).parent
    src_dir = current_dir / "src"
    if str(src_dir) not in sys.path:
        sys.path.append(str(src_dir))

    # Import node classes
    maya_bridge_nodes = {}
    maya_bridge_display_names = {}
    
    try:
        from node_wrappers.maya_bridge import MayaBridgePreprocessor
        maya_bridge_nodes["MayaBridgePreprocessor"] = MayaBridgePreprocessor
        maya_bridge_display_names["MayaBridgePreprocessor"] = "Maya Bridge (Pose + Depth)"
        print("✅ Maya Bridge basic node loaded")
    except ImportError as e:
        print(f"⚠️ Maya Bridge basic node not available: {e}")

    try:
        from node_wrappers.maya_bridge_advanced import MayaBridgeAdvanced
        maya_bridge_nodes["MayaBridgeAdvanced"] = MayaBridgeAdvanced
        maya_bridge_display_names["MayaBridgeAdvanced"] = "Maya Bridge Advanced"
        print("✅ Maya Bridge advanced node loaded")
    except ImportError as e:
        print(f"⚠️ Maya Bridge advanced node not available: {e}")

    return maya_bridge_nodes, maya_bridge_display_names

def check_maya_bridge_dependencies():
    """Check Maya Bridge dependencies"""
    missing_deps = []
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import PIL
    except ImportError:
        missing_deps.append("Pillow")
    
    try:
        import torch
    except ImportError:
        missing_deps.append("torch")
    
    if missing_deps:
        print(f"⚠️ Maya Bridge missing dependencies: {', '.join(missing_deps)}")
        print("   Install with: pip install " + " ".join(missing_deps))
    
    return missing_deps

def print_maya_bridge_info():
    """Print Maya Bridge plugin information"""
    print("\n🎭 Maya Bridge Plugin for ComfyUI v1.0.0")
    print("   Real-time Maya to ComfyUI bridge for pose and depth data")
    print("   Category: ControlNet Preprocessors/Maya Bridge")

# Initialize when imported
if __name__ != "__main__":
    print_maya_bridge_info()
    check_maya_bridge_dependencies()
    MAYA_BRIDGE_NODES, MAYA_BRIDGE_DISPLAY_NAMES = initialize_maya_bridge()
else:
    # Direct execution for testing
    print_maya_bridge_info()
    deps = check_maya_bridge_dependencies()
    nodes, display_names = initialize_maya_bridge()
    print(f"\nLoaded {len(nodes)} Maya Bridge nodes:")
    for node_name in nodes:
        print(f"  - {display_names.get(node_name, node_name)}")
