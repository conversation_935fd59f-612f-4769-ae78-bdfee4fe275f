"""
Maya Depth Buffer Extractor
Extracts depth information from Maya viewport for ComfyUI processing
"""

import maya.cmds as cmds
import maya.OpenMaya as om
import maya.OpenMayaUI as omui
import maya.OpenMayaRender as omr
import numpy as np
from typing import Optional, Tuple, List
import tempfile
import os

class MayaDepthExtractor:
    """Extracts depth buffer from Maya viewport"""
    
    def __init__(self):
        self.current_camera = None
        self.viewport_width = 512
        self.viewport_height = 512
    
    def get_active_camera(self) -> Optional[str]:
        """Get the currently active camera"""
        try:
            # Get the active viewport panel
            panel = cmds.getPanel(withFocus=True)
            
            if cmds.getPanel(typeOf=panel) == 'modelPanel':
                camera = cmds.modelPanel(panel, query=True, camera=True)
                self.current_camera = camera
                return camera
            else:
                # Default to perspective camera
                self.current_camera = 'persp'
                return 'persp'
        except:
            self.current_camera = 'persp'
            return 'persp'
    
    def get_camera_matrices(self, camera_name: str) -> <PERSON><PERSON>[om.MMatrix, om.MMatrix]:
        """Get camera view and projection matrices"""
        try:
            # Get camera transform
            camera_transform = camera_name
            camera_shapes = cmds.listRelatives(camera_transform, shapes=True, type='camera')
            
            if not camera_shapes:
                raise ValueError(f"No camera shape found for {camera_name}")
            
            camera_shape = camera_shapes[0]
            
            # Get camera world matrix
            world_matrix = cmds.xform(camera_transform, query=True, matrix=True, worldSpace=True)
            view_matrix = om.MMatrix()
            
            # Convert to MMatrix
            for i in range(4):
                for j in range(4):
                    view_matrix.setElement(i, j, world_matrix[i*4 + j])
            
            # Invert to get view matrix
            view_matrix = view_matrix.inverse()
            
            # Get projection matrix parameters
            focal_length = cmds.getAttr(camera_shape + '.focalLength')
            h_aperture = cmds.getAttr(camera_shape + '.horizontalFilmAperture') * 25.4  # Convert to mm
            v_aperture = cmds.getAttr(camera_shape + '.verticalFilmAperture') * 25.4
            near_clip = cmds.getAttr(camera_shape + '.nearClipPlane')
            far_clip = cmds.getAttr(camera_shape + '.farClipPlane')
            
            # Calculate projection matrix
            aspect_ratio = h_aperture / v_aperture
            fov_y = 2.0 * np.arctan(v_aperture / (2.0 * focal_length))
            
            proj_matrix = self._create_projection_matrix(fov_y, aspect_ratio, near_clip, far_clip)
            
            return view_matrix, proj_matrix
            
        except Exception as e:
            print(f"Error getting camera matrices: {e}")
            # Return identity matrices as fallback
            return om.MMatrix(), om.MMatrix()
    
    def _create_projection_matrix(self, fov_y: float, aspect: float, 
                                 near: float, far: float) -> om.MMatrix:
        """Create OpenGL-style projection matrix"""
        f = 1.0 / np.tan(fov_y / 2.0)
        
        proj_matrix = om.MMatrix()
        proj_matrix.setElement(0, 0, f / aspect)
        proj_matrix.setElement(1, 1, f)
        proj_matrix.setElement(2, 2, (far + near) / (near - far))
        proj_matrix.setElement(2, 3, (2.0 * far * near) / (near - far))
        proj_matrix.setElement(3, 2, -1.0)
        proj_matrix.setElement(3, 3, 0.0)
        
        return proj_matrix
    
    def extract_depth_from_scene(self, width: int = 512, height: int = 512) -> Optional[np.ndarray]:
        """Extract depth information by analyzing scene geometry"""
        try:
            camera = self.get_active_camera()
            if not camera:
                return None
            
            # Get all visible mesh objects
            visible_meshes = self._get_visible_meshes()
            if not visible_meshes:
                return None
            
            # Get camera matrices
            view_matrix, proj_matrix = self.get_camera_matrices(camera)
            
            # Create depth buffer
            depth_buffer = np.full((height, width), 1.0, dtype=np.float32)  # Initialize to far plane
            
            # Process each mesh
            for mesh in visible_meshes:
                mesh_depth = self._calculate_mesh_depth(mesh, view_matrix, proj_matrix, width, height)
                if mesh_depth is not None:
                    # Combine with existing depth buffer (take minimum depth)
                    depth_buffer = np.minimum(depth_buffer, mesh_depth)
            
            return depth_buffer
            
        except Exception as e:
            print(f"Error extracting depth from scene: {e}")
            return None
    
    def _get_visible_meshes(self) -> List[str]:
        """Get list of visible mesh objects in the scene"""
        try:
            # Get all mesh transforms
            all_meshes = cmds.ls(type='mesh', long=True)
            visible_meshes = []
            
            for mesh in all_meshes:
                # Get transform node
                transforms = cmds.listRelatives(mesh, parent=True, fullPath=True)
                if transforms:
                    transform = transforms[0]
                    
                    # Check if object is visible
                    if (cmds.getAttr(transform + '.visibility') and 
                        not cmds.getAttr(transform + '.template') and
                        not cmds.getAttr(transform + '.intermediateObject')):
                        visible_meshes.append(transform)
            
            return visible_meshes
            
        except Exception as e:
            print(f"Error getting visible meshes: {e}")
            return []
    
    def _calculate_mesh_depth(self, mesh_transform: str, view_matrix: om.MMatrix, 
                             proj_matrix: om.MMatrix, width: int, height: int) -> Optional[np.ndarray]:
        """Calculate depth buffer for a specific mesh"""
        try:
            # Get mesh vertices in world space
            mesh_shapes = cmds.listRelatives(mesh_transform, shapes=True, type='mesh')
            if not mesh_shapes:
                return None
            
            mesh_shape = mesh_shapes[0]
            
            # Get vertex positions
            vertices = cmds.xform(mesh_shape + '.vtx[*]', query=True, worldSpace=True, translation=True)
            if not vertices:
                return None
            
            # Convert to numpy array and reshape
            vertices = np.array(vertices).reshape(-1, 3)
            
            # Transform vertices to camera space
            camera_vertices = []
            for vertex in vertices:
                # Convert to homogeneous coordinates
                world_pos = om.MPoint(vertex[0], vertex[1], vertex[2], 1.0)
                
                # Transform to camera space
                camera_pos = world_pos * view_matrix
                camera_vertices.append([camera_pos.x, camera_pos.y, camera_pos.z])
            
            camera_vertices = np.array(camera_vertices)
            
            # Project to screen space
            screen_vertices = []
            for vertex in camera_vertices:
                if vertex[2] < 0:  # Only process vertices in front of camera
                    # Apply projection
                    clip_pos = om.MPoint(vertex[0], vertex[1], vertex[2], 1.0) * proj_matrix
                    
                    if clip_pos.w != 0:
                        # Normalize device coordinates
                        ndc_x = clip_pos.x / clip_pos.w
                        ndc_y = clip_pos.y / clip_pos.w
                        ndc_z = clip_pos.z / clip_pos.w
                        
                        # Convert to screen coordinates
                        screen_x = int((ndc_x + 1.0) * 0.5 * width)
                        screen_y = int((1.0 - ndc_y) * 0.5 * height)  # Flip Y
                        
                        if 0 <= screen_x < width and 0 <= screen_y < height:
                            # Normalize depth to 0-1 range
                            depth = (ndc_z + 1.0) * 0.5
                            screen_vertices.append([screen_x, screen_y, depth])
            
            if not screen_vertices:
                return None
            
            # Create depth buffer for this mesh
            mesh_depth = np.full((height, width), 1.0, dtype=np.float32)
            
            # Rasterize vertices (simple point-based approach)
            for vertex in screen_vertices:
                x, y, depth = int(vertex[0]), int(vertex[1]), vertex[2]
                if 0 <= x < width and 0 <= y < height:
                    mesh_depth[y, x] = min(mesh_depth[y, x], depth)
            
            # Simple dilation to fill gaps
            mesh_depth = self._dilate_depth(mesh_depth)
            
            return mesh_depth
            
        except Exception as e:
            print(f"Error calculating mesh depth: {e}")
            return None
    
    def _dilate_depth(self, depth_buffer: np.ndarray, iterations: int = 2) -> np.ndarray:
        """Simple depth buffer dilation to fill small gaps"""
        try:
            result = depth_buffer.copy()
            
            for _ in range(iterations):
                # Create a slightly dilated version
                dilated = result.copy()
                
                for y in range(1, result.shape[0] - 1):
                    for x in range(1, result.shape[1] - 1):
                        if result[y, x] == 1.0:  # Empty pixel
                            # Check neighbors
                            neighbors = [
                                result[y-1, x], result[y+1, x],
                                result[y, x-1], result[y, x+1]
                            ]
                            valid_neighbors = [n for n in neighbors if n < 1.0]
                            
                            if valid_neighbors:
                                dilated[y, x] = min(valid_neighbors)
                
                result = dilated
            
            return result
            
        except Exception as e:
            print(f"Error dilating depth buffer: {e}")
            return depth_buffer
    
    def extract_depth_buffer_simple(self, width: int = 512, height: int = 512) -> np.ndarray:
        """Simple depth buffer extraction using object distances"""
        try:
            camera = self.get_active_camera()
            if not camera:
                return np.ones((height, width), dtype=np.float32)
            
            # Get camera position
            camera_pos = cmds.xform(camera, query=True, worldSpace=True, translation=True)
            camera_pos = np.array(camera_pos)
            
            # Get all visible objects and their distances
            visible_meshes = self._get_visible_meshes()
            
            if not visible_meshes:
                return np.ones((height, width), dtype=np.float32)
            
            # Calculate distances and create simple depth map
            distances = []
            for mesh in visible_meshes:
                mesh_pos = cmds.xform(mesh, query=True, worldSpace=True, translation=True)
                mesh_pos = np.array(mesh_pos)
                distance = np.linalg.norm(mesh_pos - camera_pos)
                distances.append(distance)
            
            if not distances:
                return np.ones((height, width), dtype=np.float32)
            
            # Normalize distances
            min_dist = min(distances)
            max_dist = max(distances)
            
            if max_dist == min_dist:
                return np.full((height, width), 0.5, dtype=np.float32)
            
            # Create a simple gradient depth map
            # This is a placeholder - in practice, you'd want more sophisticated depth calculation
            depth_buffer = np.zeros((height, width), dtype=np.float32)
            
            # Create radial gradient from center
            center_x, center_y = width // 2, height // 2
            for y in range(height):
                for x in range(width):
                    dist_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                    max_dist_from_center = np.sqrt(center_x**2 + center_y**2)
                    normalized_dist = dist_from_center / max_dist_from_center
                    depth_buffer[y, x] = normalized_dist
            
            return depth_buffer
            
        except Exception as e:
            print(f"Error in simple depth extraction: {e}")
            return np.ones((height, width), dtype=np.float32)

# Global extractor instance
_depth_extractor = None

def get_depth_extractor() -> MayaDepthExtractor:
    """Get global depth extractor instance"""
    global _depth_extractor
    if _depth_extractor is None:
        _depth_extractor = MayaDepthExtractor()
    return _depth_extractor

def extract_maya_depth_buffer(width: int = 512, height: int = 512) -> Optional[List[float]]:
    """Extract depth buffer from Maya viewport"""
    extractor = get_depth_extractor()
    
    # Try advanced depth extraction first
    depth_buffer = extractor.extract_depth_from_scene(width, height)
    
    if depth_buffer is None:
        # Fall back to simple method
        depth_buffer = extractor.extract_depth_buffer_simple(width, height)
    
    # Convert to list for JSON serialization
    return depth_buffer.flatten().tolist() if depth_buffer is not None else None
