[project]
name = "comfyui_controlnet_aux"
description = "Plug-and-play ComfyUI node sets for making ControlNet hint images"

version = "1.1.0"
dependencies = ["torch", "importlib_metadata", "huggingface_hub", "scipy", "opencv-python>=********", "filelock", "numpy", "Pillow", "einops", "torchvision", "pyyaml", "scikit-image", "python-dateutil", "mediapipe", "svglib", "fvcore", "yapf", "omegaconf", "ftfy", "addict", "yacs", "trimesh[easy]", "albumentations", "scikit-learn", "matplotlib"]

[project.urls]
Repository = "https://github.com/Fannovel16/comfyui_controlnet_aux"

[tool.comfy]
PublisherId = "fannovel16"
DisplayName = "comfyui_controlnet_aux"
Icon = ""
