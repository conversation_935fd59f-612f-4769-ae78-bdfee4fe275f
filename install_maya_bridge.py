#!/usr/bin/env python3
"""
Maya Bridge Installation and Setup Script
Installs and configures the Maya Bridge plugin for ComfyUI
"""

import os
import sys
import json
import shutil
import subprocess
from pathlib import Path
from typing import Dict, List, Optional

class MayaBridgeInstaller:
    """Installer for Maya Bridge plugin"""
    
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.comfyui_dir = self._find_comfyui_directory()
        self.maya_scripts_dir = self._find_maya_scripts_directory()
        
    def _find_comfyui_directory(self) -> Optional[Path]:
        """Find ComfyUI installation directory"""
        # Check if we're already in ComfyUI custom_nodes
        current_dir = Path.cwd()
        if "custom_nodes" in str(current_dir):
            return current_dir.parent
        
        # Check common installation paths
        common_paths = [
            Path.home() / "ComfyUI",
            Path("C:/ComfyUI") if os.name == 'nt' else Path("/opt/ComfyUI"),
            current_dir.parent,
            current_dir
        ]
        
        for path in common_paths:
            if path.exists() and (path / "main.py").exists():
                return path
        
        return None
    
    def _find_maya_scripts_directory(self) -> Optional[Path]:
        """Find Maya scripts directory"""
        if os.name == 'nt':  # Windows
            maya_dirs = [
                Path.home() / "Documents" / "maya",
                Path(os.environ.get("MAYA_APP_DIR", "")) if "MAYA_APP_DIR" in os.environ else None
            ]
        else:  # macOS/Linux
            maya_dirs = [
                Path.home() / "maya",
                Path.home() / "Library" / "Preferences" / "Autodesk" / "maya" if sys.platform == "darwin" else None
            ]
        
        maya_dirs = [d for d in maya_dirs if d and d.exists()]
        
        # Find the most recent Maya version
        for maya_dir in maya_dirs:
            if maya_dir.exists():
                version_dirs = [d for d in maya_dir.iterdir() if d.is_dir() and d.name.replace(".", "").isdigit()]
                if version_dirs:
                    latest_version = max(version_dirs, key=lambda x: x.name)
                    scripts_dir = latest_version / "scripts"
                    if scripts_dir.exists():
                        return scripts_dir
        
        return None
    
    def install(self) -> bool:
        """Install Maya Bridge plugin"""
        print("🚀 Installing Maya Bridge Plugin for ComfyUI...")
        
        try:
            # Check prerequisites
            if not self._check_prerequisites():
                return False
            
            # Install ComfyUI components
            if not self._install_comfyui_components():
                return False
            
            # Install Maya components
            if not self._install_maya_components():
                print("⚠️ Maya components installation failed, but ComfyUI components are installed")
                print("   You can manually copy Maya scripts later")
            
            # Create configuration
            if not self._create_default_config():
                print("⚠️ Failed to create default configuration")
            
            # Install dependencies
            if not self._install_dependencies():
                print("⚠️ Some dependencies may not be installed correctly")
            
            print("✅ Maya Bridge Plugin installation completed!")
            self._print_usage_instructions()
            
            return True
            
        except Exception as e:
            print(f"❌ Installation failed: {e}")
            return False
    
    def _check_prerequisites(self) -> bool:
        """Check installation prerequisites"""
        print("🔍 Checking prerequisites...")
        
        if not self.comfyui_dir:
            print("❌ ComfyUI installation not found")
            print("   Please ensure ComfyUI is installed and accessible")
            return False
        
        print(f"✅ Found ComfyUI at: {self.comfyui_dir}")
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8 or higher is required")
            return False
        
        print(f"✅ Python version: {sys.version}")
        
        return True
    
    def _install_comfyui_components(self) -> bool:
        """Install ComfyUI plugin components"""
        print("📦 Installing ComfyUI components...")
        
        try:
            # Create target directories
            custom_nodes_dir = self.comfyui_dir / "custom_nodes" / "comfyui_maya_bridge"
            custom_nodes_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy plugin files
            files_to_copy = [
                ("node_wrappers/maya_bridge.py", "node_wrappers/maya_bridge.py"),
                ("node_wrappers/maya_bridge_advanced.py", "node_wrappers/maya_bridge_advanced.py"),
                ("src/maya_connector.py", "src/maya_connector.py"),
                ("src/bone_mapping_presets.py", "src/bone_mapping_presets.py"),
                ("src/maya_bridge_config.py", "src/maya_bridge_config.py"),
                ("__init__.py", "__init__.py")
            ]
            
            for src_file, dst_file in files_to_copy:
                src_path = self.script_dir / src_file
                dst_path = custom_nodes_dir / dst_file
                
                if src_path.exists():
                    dst_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(src_path, dst_path)
                    print(f"  ✅ Copied {src_file}")
                else:
                    print(f"  ⚠️ Source file not found: {src_file}")
            
            # Create __init__.py if it doesn't exist
            init_file = custom_nodes_dir / "__init__.py"
            if not init_file.exists():
                with open(init_file, 'w') as f:
                    f.write('"""Maya Bridge Plugin for ComfyUI"""\n')
            
            print("✅ ComfyUI components installed")
            return True
            
        except Exception as e:
            print(f"❌ Failed to install ComfyUI components: {e}")
            return False
    
    def _install_maya_components(self) -> bool:
        """Install Maya script components"""
        print("🎭 Installing Maya components...")
        
        if not self.maya_scripts_dir:
            print("⚠️ Maya scripts directory not found")
            print("   Please manually copy maya_scripts/* to your Maya scripts directory")
            return False
        
        try:
            # Copy Maya scripts
            maya_files = [
                "comfyui_bridge_server.py",
                "depth_buffer_extractor.py"
            ]
            
            for maya_file in maya_files:
                src_path = self.script_dir / "maya_scripts" / maya_file
                dst_path = self.maya_scripts_dir / maya_file
                
                if src_path.exists():
                    shutil.copy2(src_path, dst_path)
                    print(f"  ✅ Copied {maya_file} to Maya scripts")
                else:
                    print(f"  ⚠️ Maya script not found: {maya_file}")
            
            # Create Maya shelf script
            shelf_script = self._create_maya_shelf_script()
            shelf_path = self.maya_scripts_dir / "comfyui_bridge_shelf.py"
            
            with open(shelf_path, 'w') as f:
                f.write(shelf_script)
            
            print(f"✅ Maya components installed to: {self.maya_scripts_dir}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to install Maya components: {e}")
            return False
    
    def _create_maya_shelf_script(self) -> str:
        """Create Maya shelf setup script"""
        return '''"""
ComfyUI Bridge Shelf Setup for Maya
Run this script in Maya to create the ComfyUI Bridge shelf
"""

import maya.cmds as cmds

def create_comfyui_bridge_shelf():
    """Create ComfyUI Bridge shelf"""
    shelf_name = "ComfyUI_Bridge"
    
    # Delete existing shelf if it exists
    if cmds.shelfLayout(shelf_name, exists=True):
        cmds.deleteUI(shelf_name)
    
    # Create new shelf
    shelf = cmds.shelfLayout(shelf_name, parent="ShelfLayout")
    
    # Start Bridge Server button
    cmds.shelfButton(
        parent=shelf,
        label="Start Bridge",
        command="exec(open('comfyui_bridge_server.py').read()); start_comfyui_bridge()",
        annotation="Start ComfyUI Bridge Server",
        image="play.png"
    )
    
    # Stop Bridge Server button
    cmds.shelfButton(
        parent=shelf,
        label="Stop Bridge",
        command="stop_comfyui_bridge()",
        annotation="Stop ComfyUI Bridge Server",
        image="stop.png"
    )
    
    # Status button
    cmds.shelfButton(
        parent=shelf,
        label="Status",
        command="print(get_bridge_status())",
        annotation="Check Bridge Server Status",
        image="info.png"
    )
    
    print(f"Created {shelf_name} shelf")

# Auto-create shelf
create_comfyui_bridge_shelf()
'''
    
    def _create_default_config(self) -> bool:
        """Create default configuration file"""
        print("⚙️ Creating default configuration...")
        
        try:
            config_dir = self.comfyui_dir / "custom_nodes" / "comfyui_maya_bridge" / "config"
            config_dir.mkdir(parents=True, exist_ok=True)
            
            config_file = config_dir / "maya_bridge_config.json"
            
            default_config = {
                "maya_connection": {
                    "default_host": "localhost",
                    "default_port": 7001,
                    "timeout": 5.0
                },
                "bone_mapping": {
                    "default_preset": "standard",
                    "auto_detect": True
                },
                "depth_processing": {
                    "default_model": "depth_anything_v2_vitl.pth",
                    "max_depth": 20.0
                },
                "rendering": {
                    "default_resolution": 512
                }
            }
            
            with open(config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
            
            print(f"✅ Configuration created: {config_file}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to create configuration: {e}")
            return False
    
    def _install_dependencies(self) -> bool:
        """Install Python dependencies"""
        print("📚 Installing dependencies...")
        
        try:
            # Check if pip is available
            subprocess.run([sys.executable, "-m", "pip", "--version"], 
                          check=True, capture_output=True)
            
            # Install required packages
            packages = [
                "numpy",
                "opencv-python",
                "Pillow",
                "torch",
                "torchvision"
            ]
            
            for package in packages:
                try:
                    subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                  check=True, capture_output=True)
                    print(f"  ✅ Installed {package}")
                except subprocess.CalledProcessError:
                    print(f"  ⚠️ Failed to install {package}")
            
            return True
            
        except subprocess.CalledProcessError:
            print("⚠️ pip not available, please install dependencies manually")
            return False
    
    def _print_usage_instructions(self):
        """Print usage instructions"""
        print("\n" + "="*60)
        print("🎉 MAYA BRIDGE PLUGIN INSTALLATION COMPLETE!")
        print("="*60)
        print("\n📋 NEXT STEPS:")
        print("\n1. Maya Setup:")
        print("   - Open Maya")
        print("   - Run the following in Maya's Script Editor:")
        print("     exec(open('comfyui_bridge_shelf.py').read())")
        print("   - This will create a ComfyUI Bridge shelf")
        print("\n2. ComfyUI Setup:")
        print("   - Restart ComfyUI")
        print("   - Look for 'Maya Bridge' nodes in the ControlNet Preprocessors category")
        print("\n3. Usage:")
        print("   - In Maya: Click 'Start Bridge' on the shelf")
        print("   - In ComfyUI: Add a Maya Bridge node to your workflow")
        print("   - Configure bone mapping and connection settings")
        print("\n4. Troubleshooting:")
        print("   - Ensure Maya and ComfyUI are on the same network")
        print("   - Check firewall settings for port 7001")
        print("   - Enable debug mode in the Maya Bridge node for detailed logs")
        print("\n📖 For more information, check the documentation.")
        print("="*60)

def main():
    """Main installation function"""
    installer = MayaBridgeInstaller()
    success = installer.install()
    
    if not success:
        print("\n❌ Installation failed. Please check the errors above.")
        sys.exit(1)
    
    print("\n✅ Installation completed successfully!")

if __name__ == "__main__":
    main()
