"""
Advanced Maya Bridge Node for ComfyUI
Provides real-time Maya integration with advanced features
"""

from ..utils import common_annotator_call, INPUT, define_preprocessor_inputs
import comfy.model_management as model_management
import numpy as np
import json
import cv2
import time
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image

class MayaBridgeAdvanced:
    """Advanced Maya Bridge node with real-time capabilities"""
    
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                # Connection settings
                "maya_host": ("STRING", {"default": "localhost"}),
                "maya_port": ("INT", {"default": 7001, "min": 1000, "max": 65535}),
                "connection_timeout": ("FLOAT", {"default": 5.0, "min": 1.0, "max": 30.0, "step": 0.5}),
                
                # Bone mapping
                "bone_mapping_preset": (["custom", "standard", "mixamo", "humanik", "advanced_skeleton"], {"default": "standard"}),
                "auto_detect_bones": (["disable", "enable"], {"default": "enable"}),
                "ignore_missing_bones": (["disable", "enable"], {"default": "enable"}),
                
                # Pose processing
                "character_facing": (["auto", "front", "back"], {"default": "auto"}),
                "flip_horizontal": (["disable", "enable"], {"default": "disable"}),
                "pose_smoothing": (["disable", "enable"], {"default": "disable"}),
                "smoothing_factor": ("FLOAT", {"default": 0.3, "min": 0.1, "max": 0.9, "step": 0.1}),
                
                # Depth processing
                "enable_depth": (["enable", "disable"], {"default": "enable"}),
                "depth_source": (["maya_buffer", "generated", "auto"], {"default": "auto"}),
                "depth_model": (["depth_anything_v2_vitl.pth", "depth_anything_v2_vitb.pth", "depth_anything_v2_vits.pth"], {"default": "depth_anything_v2_vitl.pth"}),
                "max_depth": ("FLOAT", {"default": 20.0, "min": 0.1, "max": 100.0, "step": 0.1}),
                
                # Output settings
                "resolution": ("INT", {"default": 512, "min": 64, "max": 2048, "step": 64}),
                "output_format": (["combined", "pose_only", "depth_only"], {"default": "combined"}),
                "real_time_mode": (["disable", "enable"], {"default": "disable"}),
                "frame_rate": ("INT", {"default": 30, "min": 1, "max": 60}),
                
                # Debug options
                "debug_mode": (["disable", "enable"], {"default": "disable"}),
                "save_debug_images": (["disable", "enable"], {"default": "disable"})
            },
            "optional": {
                "custom_bone_mapping": ("STRING", {"multiline": True, "default": "{}"}),
                "trigger_capture": ("BOOLEAN", {"default": False}),
                "reference_image": ("IMAGE",)
            }
        }
    
    RETURN_TYPES = ("IMAGE", "IMAGE", "POSE_KEYPOINT", "STRING", "BOOLEAN")
    RETURN_NAMES = ("pose_image", "depth_image", "pose_keypoints", "status_info", "connection_status")
    FUNCTION = "process_maya_bridge"
    CATEGORY = "ControlNet Preprocessors/Maya Bridge"
    
    def __init__(self):
        self.last_capture_time = 0
        self.cached_data = None
        self.connection_status = False
        self.maya_connector = None
        self.pose_detector = None
        self.depth_detector = None
        self.frame_buffer = []
        self.smoothing_enabled = False
    
    def process_maya_bridge(self, maya_host="localhost", maya_port=7001, connection_timeout=5.0,
                           bone_mapping_preset="standard", auto_detect_bones="enable", 
                           ignore_missing_bones="enable", character_facing="auto", 
                           flip_horizontal="disable", pose_smoothing="disable", smoothing_factor=0.3,
                           enable_depth="enable", depth_source="auto", 
                           depth_model="depth_anything_v2_vitl.pth", max_depth=20.0,
                           resolution=512, output_format="combined", real_time_mode="disable", 
                           frame_rate=30, debug_mode="disable", save_debug_images="disable",
                           custom_bone_mapping="{}", trigger_capture=False, reference_image=None):
        
        start_time = time.time()
        status_info = []
        
        try:
            # Check if we should capture new data
            should_capture = self._should_capture_new_data(real_time_mode, frame_rate, trigger_capture)
            
            if should_capture or self.cached_data is None:
                # Get Maya data
                maya_data = self._get_maya_data_with_timeout(maya_host, maya_port, connection_timeout)
                
                if maya_data:
                    self.connection_status = True
                    self.cached_data = maya_data
                    status_info.append(f"✓ Connected to Maya at {maya_host}:{maya_port}")
                else:
                    self.connection_status = False
                    status_info.append(f"✗ Failed to connect to Maya")
                    
                    # Use reference image if available
                    if reference_image is not None:
                        return self._process_reference_image(reference_image, resolution, status_info)
                    else:
                        return self._create_fallback_output(resolution, status_info)
            else:
                status_info.append("📋 Using cached Maya data")
            
            # Get bone mapping
            bone_map = self._get_bone_mapping_advanced(bone_mapping_preset, custom_bone_mapping, status_info)
            
            # Auto-detect bones if enabled
            if auto_detect_bones == "enable" and self.cached_data.get("bone_positions"):
                bone_map = self._auto_detect_bone_mapping_advanced(
                    list(self.cached_data["bone_positions"].keys()), 
                    bone_map, status_info
                )
            
            # Process pose data
            pose_image, pose_keypoints = self._process_pose_data_advanced(
                self.cached_data, bone_map, ignore_missing_bones == "enable",
                character_facing, flip_horizontal == "enable",
                pose_smoothing == "enable", smoothing_factor,
                resolution, status_info
            )
            
            # Process depth data
            depth_image = None
            if enable_depth == "enable":
                depth_image = self._process_depth_data_advanced(
                    self.cached_data, depth_source, depth_model, max_depth, 
                    resolution, status_info
                )
            
            # Save debug images if enabled
            if save_debug_images == "enable" and debug_mode == "enable":
                self._save_debug_images(pose_image, depth_image, pose_keypoints)
            
            # Prepare output based on format
            if output_format == "pose_only":
                output_images = (pose_image, pose_image)
            elif output_format == "depth_only" and depth_image is not None:
                output_images = (depth_image, depth_image)
            else:
                # Combined output
                if depth_image is None:
                    depth_image = pose_image
                output_images = (pose_image, depth_image)
            
            # Add performance info
            processing_time = time.time() - start_time
            status_info.append(f"⏱️ Processing time: {processing_time:.3f}s")
            
            if debug_mode == "enable":
                status_info.extend(self._get_debug_info())
            
            return (*output_images, pose_keypoints, "\n".join(status_info), self.connection_status)
            
        except Exception as e:
            status_info.append(f"❌ Error: {str(e)}")
            return self._create_fallback_output(resolution, status_info)
    
    def _should_capture_new_data(self, real_time_mode: str, frame_rate: int, trigger_capture: bool) -> bool:
        """Determine if new data should be captured"""
        if trigger_capture:
            return True
        
        if real_time_mode == "enable":
            current_time = time.time()
            frame_interval = 1.0 / frame_rate
            
            if current_time - self.last_capture_time >= frame_interval:
                self.last_capture_time = current_time
                return True
        
        return False
    
    def _get_maya_data_with_timeout(self, host: str, port: int, timeout: float) -> Optional[Dict]:
        """Get Maya data with connection timeout"""
        try:
            # Import Maya connector with timeout
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
            from maya_connector import get_maya_connector
            
            # Get connector with timeout
            connector = get_maya_connector(host, port)
            connector.timeout = timeout
            
            # Get scene data
            scene_data = connector.get_scene_data()
            return scene_data
            
        except Exception as e:
            print(f"Maya connection error: {e}")
            return None
    
    def _get_bone_mapping_advanced(self, preset_name: str, custom_mapping: str, 
                                  status_info: List[str]) -> Dict[str, int]:
        """Get bone mapping with advanced validation"""
        try:
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
            from bone_mapping_presets import get_bone_mapping_manager
            
            manager = get_bone_mapping_manager()
            
            if preset_name == "custom" and custom_mapping.strip():
                try:
                    bone_map = json.loads(custom_mapping)
                    is_valid, errors = manager.validate_mapping(bone_map)
                    
                    if is_valid:
                        status_info.append(f"✓ Using custom bone mapping ({len(bone_map)} bones)")
                        return bone_map
                    else:
                        status_info.append(f"⚠️ Custom mapping invalid: {', '.join(errors)}")
                        # Fall back to preset
                        preset = manager.get_preset("standard")
                        return preset["mapping"] if preset else {}
                        
                except json.JSONDecodeError as e:
                    status_info.append(f"⚠️ Custom mapping JSON error: {str(e)}")
                    preset = manager.get_preset("standard")
                    return preset["mapping"] if preset else {}
            else:
                preset = manager.get_preset(preset_name)
                if preset:
                    status_info.append(f"✓ Using {preset['name']} preset ({len(preset['mapping'])} bones)")
                    return preset["mapping"]
                else:
                    status_info.append(f"⚠️ Preset '{preset_name}' not found, using standard")
                    preset = manager.get_preset("standard")
                    return preset["mapping"] if preset else {}
                    
        except Exception as e:
            status_info.append(f"⚠️ Bone mapping error: {str(e)}")
            return {}
    
    def _auto_detect_bone_mapping_advanced(self, available_bones: List[str], 
                                          current_mapping: Dict[str, int], 
                                          status_info: List[str]) -> Dict[str, int]:
        """Advanced auto-detection with confidence scoring"""
        try:
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
            from bone_mapping_presets import get_bone_mapping_manager
            
            manager = get_bone_mapping_manager()
            suggestions = manager.suggest_bone_names(available_bones, "standard")
            
            updated_mapping = current_mapping.copy()
            detected_count = 0
            confidence_scores = []
            
            for openpose_name, bone_candidates in suggestions.items():
                if bone_candidates:
                    # Calculate confidence based on name similarity
                    best_match = bone_candidates[0]
                    confidence = self._calculate_name_similarity(openpose_name.lower(), best_match.lower())
                    confidence_scores.append(confidence)
                    
                    # Find OpenPose index
                    openpose_info = manager.get_all_openpose_info()
                    for idx, info in openpose_info.items():
                        if info["name"] == openpose_name:
                            updated_mapping[best_match] = idx
                            detected_count += 1
                            break
            
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
            status_info.append(f"🔍 Auto-detected {detected_count} bones (confidence: {avg_confidence:.2f})")
            
            return updated_mapping
            
        except Exception as e:
            status_info.append(f"⚠️ Auto-detection failed: {str(e)}")
            return current_mapping
    
    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between bone names"""
        # Simple similarity based on common substrings
        name1_parts = set(name1.lower().split('_'))
        name2_parts = set(name2.lower().split('_'))
        
        if not name1_parts or not name2_parts:
            return 0.0
        
        intersection = name1_parts.intersection(name2_parts)
        union = name1_parts.union(name2_parts)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _process_pose_data_advanced(self, maya_data: Dict, bone_map: Dict, 
                                   ignore_missing: bool, facing: str, flip_h: bool,
                                   smoothing: bool, smoothing_factor: float,
                                   resolution: int, status_info: List[str]) -> Tuple[Any, Any]:
        """Advanced pose processing with smoothing"""
        
        # Map Maya bones to OpenPose keypoints
        keypoints = self._map_maya_bones_to_openpose_advanced(
            maya_data.get("bone_positions", {}), 
            bone_map, ignore_missing, status_info
        )
        
        # Apply smoothing if enabled
        if smoothing and len(self.frame_buffer) > 0:
            keypoints = self._apply_pose_smoothing(keypoints, smoothing_factor)
        
        # Store in frame buffer for smoothing
        self.frame_buffer.append(keypoints)
        if len(self.frame_buffer) > 5:  # Keep last 5 frames
            self.frame_buffer.pop(0)
        
        # Determine character facing
        if facing == "auto":
            facing_direction = self._detect_character_facing_advanced(keypoints)
            status_info.append(f"👤 Detected facing: {facing_direction}")
        else:
            facing_direction = facing
        
        # Apply horizontal flip if needed
        if flip_h or facing_direction == "back":
            keypoints = self._flip_keypoints_horizontal_advanced(keypoints)
            status_info.append("🔄 Applied horizontal flip")
        
        # Generate pose image
        pose_image = self._generate_pose_image_advanced(keypoints, resolution)
        
        # Create pose keypoints JSON
        pose_json = self._create_openpose_json_advanced(keypoints, resolution, resolution)
        
        return pose_image, pose_json
    
    def _process_depth_data_advanced(self, maya_data: Dict, depth_source: str, 
                                    model_name: str, max_depth: float, 
                                    resolution: int, status_info: List[str]) -> Optional[Any]:
        """Advanced depth processing with multiple sources"""
        
        try:
            depth_image = None
            
            if depth_source == "maya_buffer" or depth_source == "auto":
                # Try Maya depth buffer first
                depth_buffer = maya_data.get("depth_buffer")
                if depth_buffer is not None:
                    depth_image = self._process_maya_depth_buffer_advanced(
                        depth_buffer, max_depth, resolution
                    )
                    status_info.append("🎯 Using Maya depth buffer")
                elif depth_source == "maya_buffer":
                    status_info.append("⚠️ Maya depth buffer not available")
            
            if depth_image is None and (depth_source == "generated" or depth_source == "auto"):
                # Generate depth using DepthAnything
                viewport_image = maya_data.get("viewport_image")
                if viewport_image is not None:
                    from custom_controlnet_aux.depth_anything_v2 import DepthAnythingV2Detector
                    
                    if self.depth_detector is None:
                        self.depth_detector = DepthAnythingV2Detector.from_pretrained(
                            filename=model_name
                        ).to(model_management.get_torch_device())
                    
                    depth_image = self.depth_detector(
                        viewport_image, 
                        detect_resolution=resolution, 
                        max_depth=max_depth
                    )
                    status_info.append("🤖 Generated depth using DepthAnythingV2")
                else:
                    status_info.append("⚠️ No viewport image for depth generation")
            
            return depth_image
            
        except Exception as e:
            status_info.append(f"❌ Depth processing error: {str(e)}")
            return None
    
    def _apply_pose_smoothing(self, current_keypoints: List, smoothing_factor: float) -> List:
        """Apply temporal smoothing to pose keypoints"""
        if not self.frame_buffer:
            return current_keypoints
        
        smoothed_keypoints = []
        prev_keypoints = self.frame_buffer[-1]
        
        for i, current_kp in enumerate(current_keypoints):
            if current_kp and i < len(prev_keypoints) and prev_keypoints[i]:
                # Linear interpolation between previous and current
                prev_kp = prev_keypoints[i]
                smoothed_x = prev_kp[0] * smoothing_factor + current_kp[0] * (1 - smoothing_factor)
                smoothed_y = prev_kp[1] * smoothing_factor + current_kp[1] * (1 - smoothing_factor)
                smoothed_keypoints.append((smoothed_x, smoothed_y))
            else:
                smoothed_keypoints.append(current_kp)
        
        return smoothed_keypoints
    
    def _get_debug_info(self) -> List[str]:
        """Get debug information"""
        debug_info = []
        
        if self.cached_data:
            debug_info.append(f"🔧 Cached data timestamp: {self.cached_data.get('timestamp', 'N/A')}")
            debug_info.append(f"🔧 Available bones: {len(self.cached_data.get('bone_positions', {}))}")
            debug_info.append(f"🔧 Has viewport image: {self.cached_data.get('viewport_image') is not None}")
            debug_info.append(f"🔧 Has depth buffer: {self.cached_data.get('depth_buffer') is not None}")
        
        debug_info.append(f"🔧 Frame buffer size: {len(self.frame_buffer)}")
        debug_info.append(f"🔧 Connection status: {self.connection_status}")
        
        return debug_info
    
    def _save_debug_images(self, pose_image: Any, depth_image: Any, pose_keypoints: Any):
        """Save debug images to disk"""
        try:
            import os
            from datetime import datetime
            
            debug_dir = "debug_output"
            os.makedirs(debug_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if pose_image:
                pose_image.save(os.path.join(debug_dir, f"pose_{timestamp}.png"))
            
            if depth_image:
                depth_image.save(os.path.join(debug_dir, f"depth_{timestamp}.png"))
            
            if pose_keypoints:
                with open(os.path.join(debug_dir, f"keypoints_{timestamp}.json"), 'w') as f:
                    json.dump(pose_keypoints, f, indent=2)
                    
        except Exception as e:
            print(f"Failed to save debug images: {e}")
    
    # Additional helper methods would be implemented here...
    # (Continuing with the remaining methods in the next part due to length constraints)

NODE_CLASS_MAPPINGS = {
    "MayaBridgeAdvanced": MayaBridgeAdvanced
}

NODE_DISPLAY_NAME_MAPPINGS = {
    "MayaBridgeAdvanced": "Maya Bridge Advanced"
}
