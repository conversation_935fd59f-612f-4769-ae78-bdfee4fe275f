# Copyright (c) OpenMMLab. All rights reserved.
import pickle

from .base import BaseFileHandler


class PickleHandler(BaseFileHandler):

    str_like = False

    def load_from_fileobj(self, file, **kwargs):
        return pickle.load(file, **kwargs)

    def load_from_path(self, filepath, **kwargs):
        return super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).load_from_path(
            filepath, mode='rb', **kwargs)

    def dump_to_str(self, obj, **kwargs):
        kwargs.setdefault('protocol', 2)
        return pickle.dumps(obj, **kwargs)

    def dump_to_fileobj(self, obj, file, **kwargs):
        kwargs.setdefault('protocol', 2)
        pickle.dump(obj, file, **kwargs)

    def dump_to_path(self, obj, filepath, **kwargs):
        super(<PERSON>le<PERSON><PERSON><PERSON>, self).dump_to_path(
            obj, filepath, mode='wb', **kwargs)
